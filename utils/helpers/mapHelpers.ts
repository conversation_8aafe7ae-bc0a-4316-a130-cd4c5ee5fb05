import * as THREE from 'three';

// use this to check if the ray direction is facing outwards, only display the ray if it is facing outwards
export function isDirectionFacingOut(
  point: THREE.Vector3,
  dir: [number, number, number],
  outline: THREE.Vector3[],
  index: number
): boolean {
  const prev = outline[(index - 1 + outline.length) % outline.length];
  const next = outline[(index + 1) % outline.length];

  const tangent = new THREE.Vector2(next.x - prev.x, next.z - prev.z).normalize();
  const normal = new THREE.Vector2(-tangent.y, tangent.x); // outward normal (right hand)

  const rayDir = new THREE.Vector2(dir[0], dir[2]).normalize();

  return rayDir.dot(normal) > 0; // >0 outwards, <0 towards the center
}

// use this to intersect a ray with a segment(lot boundary lines)
export function intersectRayWithSegmentXZ(
  origin: THREE.Vector3,
  dir: THREE.Vector3,
  a: THREE.Vector3,
  b: THREE.Vector3
): THREE.Vector3 | null {
  const x1 = origin.x;
  const z1 = origin.z;
  const dx1 = dir.x;
  const dz1 = dir.z;

  const x2 = a.x;
  const z2 = a.z;
  const x3 = b.x;
  const z3 = b.z;

  const dx2 = x3 - x2;
  const dz2 = z3 - z2;

  const denom = dx1 * dz2 - dz1 * dx2;
  if (Math.abs(denom) < 1e-6) return null; // parallel

  const t = ((x2 - x1) * dz2 - (z2 - z1) * dx2) / denom;
  const u = ((x2 - x1) * dz1 - (z2 - z1) * dx1) / denom;

  if (t < 0 || u < 0 || u > 1) return null; // not in the right direction or not on the segment

  return new THREE.Vector3(x1 + t * dx1, origin.y, z1 + t * dz1);
}

// use this to get the closest intersection of a ray with the lot boundary
export function getClosestIntersection(
  origin: THREE.Vector3,
  direction: THREE.Vector3,
  lotRings: number[][][]
): { point: THREE.Vector3; distance: number } | null {
  let minDistance = Infinity;
  let closest: THREE.Vector3 | null = null;
  const ringPoints = lotRings.flat();
  for (let i = 0; i < ringPoints.length; i++) {
    const a = ringPoints[i];
    const b = ringPoints[(i + 1) % ringPoints.length];
    const aPoint = new THREE.Vector3(a[0], origin.y, a[1]);
    const bPoint = new THREE.Vector3(b[0], origin.y, b[1]);
    const hit = intersectRayWithSegmentXZ(origin, direction, aPoint, bPoint);
    if (hit) {
      const dist = origin.distanceTo(hit);
      if (dist < minDistance) {
        minDistance = dist;
        closest = hit;
      }
    }
  }

  return closest ? { point: closest, distance: minDistance } : null;
}

export function lngLatToMercator(lng: number, lat: number) {
  const R = 6378137;
  const x = R * THREE.MathUtils.degToRad(lng);
  const y = R * Math.log(Math.tan(Math.PI / 4 + THREE.MathUtils.degToRad(lat) / 2));
  return { x, y };
}

export function filterClosePoints(points: THREE.Vector3[], threshold = 0.3): THREE.Vector3[] {
  if (points.length === 0) return [];

  const filtered: THREE.Vector3[] = [points[0]];

  for (let i = 1; i < points.length; i++) {
    const prev = filtered[filtered.length - 1];
    const curr = points[i];
    if (prev.distanceTo(curr) >= threshold) {
      filtered.push(curr);
    }
  }

  const first = filtered[0];
  const last = filtered[filtered.length - 1];
  if (first.distanceTo(last) < threshold) {
    filtered.pop();
  }

  return filtered;
}

export function lngLatToTile(lng: number, lat: number, zoom: number) {
  const scale = 1 << zoom;
  const tileX = Math.floor(((lng + 180) / 360) * scale);
  const sinLat = Math.sin((lat * Math.PI) / 180);
  const tileY = Math.floor((0.5 - Math.log((1 + sinLat) / (1 - sinLat)) / (4 * Math.PI)) * scale);
  return [tileX, tileY];
}

export function getPolygonMercator(
  coordinates: { lng: number; lat: number },
  data: { height: number; ring: number[][] }
) {
  const { x: x0, y: y0 } = lngLatToMercator(coordinates.lng, coordinates.lat);
  const newData = data.ring.map(([x, y]) => {
    const { x: x1, y: y1 } = lngLatToMercator(x, y);
    return [x1 - x0, -(y1 - y0)];
  });
  return { height: data.height, ring: newData };
}

export function getPolygonGeometry(
  coordinates: { lng: number; lat: number },
  data: { height: number; ring: number[][] },
  isExtruded: boolean = true
) {
  const newData = getPolygonMercator(coordinates, data);
  const outer = newData.ring.map(([x, y]) => new THREE.Vector2(x, y));
  const shape = new THREE.Shape(outer);
  const geometry = new THREE.ShapeGeometry(shape);
  const extrudedGeometry = isExtruded
    ? new THREE.ExtrudeGeometry(shape, {
        depth: -newData.height,
        bevelEnabled: false,
      })
    : geometry;
  return extrudedGeometry;
}