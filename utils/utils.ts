import { ObjectMap } from "@react-three/fiber";
import * as THREE from "three";
import { GLTF } from "three-stdlib";

let gltf: GLTF & ObjectMap;

export function getLocalTime(): string {
  //let formattedTime: string = new Date().toLocaleTimeString();
  let formattedTime: string = new Date().toLocaleString();
  return formattedTime;
}

export function getAutoScreenshotCamPostion(): [Array<{ x: number; y: number; z: number }>, THREE.Vector3] {
  if (gltf == null) {
    throw new Error("GLTF not set ");
  }

  const boundingBoxSize = getModelSize();

  const camPostionsArray: Array<{ x: number; y: number; z: number }> = [
    {
      x: -boundingBoxSize.x * 0.5,
      y: boundingBoxSize.y * 0.5,
      z: boundingBoxSize.z * 0.5,
    },

    {
      x: -boundingBoxSize.x,
      y: boundingBoxSize.y / 2,
      z: -boundingBoxSize.z * 0.5,
    },

    {
      x: boundingBoxSize.x * 0.5,
      y: boundingBoxSize.y * 0.5,
      z: -boundingBoxSize.z * 2,
    },

    {
      x: boundingBoxSize.x * 0.5,
      y: boundingBoxSize.y * 2.5,
      z: -boundingBoxSize.z * 2,
    },

    {
      x: boundingBoxSize.x * 1.5,
      y: boundingBoxSize.y * 2.5,
      z: boundingBoxSize.z * 0.5,
    },

    {
      x: boundingBoxSize.x * 0.5,
      y: boundingBoxSize.y * 2.5,
      z: boundingBoxSize.z * 0.5,
    },

    {
      x: -boundingBoxSize.x * 0.6,
      y: boundingBoxSize.y * 2.3,
      z: -boundingBoxSize.z * 0.3,
    },

    {
      x: boundingBoxSize.x * 2,
      y: boundingBoxSize.y * 2.3,
      z: -boundingBoxSize.z * 1.5,
    },
  ];

  return [camPostionsArray, boundingBoxSize];
}

export function setGltf(model: GLTF & ObjectMap) {
  gltf = model;
}

export function getGltf() {
  return gltf;
}

export function getModelSize() {
  const box = new THREE.Box3().setFromObject(gltf.scene);
  const boundingBoxSize = box.getSize(new THREE.Vector3());
  return boundingBoxSize;
}
