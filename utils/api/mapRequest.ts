import axios, { AxiosRequestConfig, Method } from "axios";

const instance = axios.create({
  baseURL: process.env.EXPO_PUBLIC_GIS_SERVER_URL || "http://localhost:4001",
  headers: {
    "Content-Type": "application/json",
  },
});

// handle errors globally
instance.interceptors.response.use(
  (response) => {
    response.data.success = true;
    return response.data;
  },
  (error) => {
    console.error("API Error:", error?.response?.data || error.message);
    throw error;
  }
);

// common request function
export async function api<T = any>(
  method: Method,
  url: string,
  data?: any,
  config?: AxiosRequestConfig,
  retry = true
): Promise<T> {
  try {
    const response = await instance.request<T>({
      method,
      url,
      data,
      ...config,
    });
    response.success = true;
    return response as unknown as T;
  } catch (error: any) {
    console.error("API Error:", error?.response?.data || error.message);
    return {
      success: false,
      message: error.message,
    } as T;
  }
}

export async function getLotBoundary({
  x,
  y,
  lat,
  lng,
  code,
  placeId,
}: {
  x?: number;
  y?: number;
  lat?: number;
  lng?: number;
  code?: string;
  placeId?: string;
}): Promise<any> {
  const params = new URLSearchParams();
  if (x) params.set("x", x.toString());
  if (y) params.set("y", y.toString());
  if (lat) params.set("lat", lat.toString());
  if (lng) params.set("lng", lng.toString());
  if (code) params.set("code", code);
  if (placeId) params.set("placeId", placeId);
  return api("GET", `/api/gis/lot-info?${params.toString()}`);
}

export async function getPlaceDetails(placeId: string, language: string = "en"): Promise<any> {
  return api("POST", "/api/gis/place-details", { placeId, language });
}

export async function getAutoCompletePlaces(
  search: string,
  types: string[],
  language: string = "en"
): Promise<any> {
  return api("POST", "/api/gis/place-autocomplete", {
    search,
    types,
    language,
  });
}
