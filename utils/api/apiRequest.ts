import axios, { AxiosRequestConfig, Method } from "axios";

const instance = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_SERVER_LOCAL_URL,
  // timeout: 10000, // set to 10 seconds, could be changed
  headers: {
    "Content-Type": "application/json",
  },
});

// handle errors globally
instance.interceptors.response.use(
  (response) => {
    response.data.success = true;
    return response.data;
  },
  (error) => {
    console.error("API Error:", error?.response?.data || error.message);
    throw error;
  }
);

// common request function
export async function apiRequest<T = any>(
  method: Method,
  url: string,
  data?: any,
  config?: AxiosRequestConfig,
  retry = true
): Promise<T> {
  try {
    const response = await instance.request<T>({
      method,
      url,
      data,
      ...config,
    });
    return response as unknown as T;
  } catch (error: any) {
    console.error("API Error:", error?.response?.data || error.message);
    return {
      success: false,
      message: error.message,
    } as T;
  }
}
