import { create } from "zustand";

export type screenShootsType = {
  isTakeScreenShot: boolean;
  setIsTakeScreenShot: (isTakeScreenShot: boolean) => void;
  isAutoshoots: boolean;
  setIsAutoshoots: (isTakeScreenShot: boolean) => void;
};

export const useScreenShootsStore = create<screenShootsType>((set) => ({
  isTakeScreenShot: false,
  setIsTakeScreenShot: (isTakeScreenShot) => set({ isTakeScreenShot }),
  isAutoshoots: false,
  setIsAutoshoots: (isTakeScreenShot) => set({ isTakeScreenShot }),
}));
