import { create } from "zustand";

export type MapStoreType = {
  zoom: number;
  setZoom: (zoom: number) => void;
  isMapOpen: boolean;
  setIsMapOpen: (isMapOpen: boolean) => void;
  places: { place_id: string; description: string }[];
  shouldAutoComplete: boolean;
  setShouldAutoComplete: (shouldAutoComplete: boolean) => void;
  search: string;
  setSearch: (search: string) => void;
  setPlaces: (places: { place_id: string; description: string }[]) => void;
  coordinates: {
    lat: number;
    lng: number;
  };
  setCoordinates: (coordinates: { lat: number; lng: number }) => void;
  mapLayerType: "satellite" | "street";
  setMapLayerType: (mapLayerType: "satellite" | "street") => void;
  lotBoundarys: { geometry: number[][][]}[];
  setLotBoundarys: (lotBoundarys: { geometry: number[][][] }[]) => void;
  placeId: string;
  setPlaceId: (placeId: string) => void;
};

export const useMapStore = create<MapStoreType>((set) => ({
  zoom: 18,
  setZoom: (zoom) => set({ zoom }),
  isMapOpen: false,
  setIsMapOpen: (isMapOpen) => set({ isMapOpen }),
  places: [],
  shouldAutoComplete: true,
  setShouldAutoComplete: (shouldAutoComplete) => set({ shouldAutoComplete }),
  search: "",
  setSearch: (search) => set({ search }),
  setPlaces: (places) => set({ places }),
  coordinates: { lat: -33.8651, lng: 151.2099 },
  setCoordinates: (coordinates) => set({ coordinates }),
  mapLayerType: "street",
  setMapLayerType: (mapLayerType) => set({ mapLayerType }),
  lotBoundarys: [],
  setLotBoundarys: (lotBoundarys) => set({ lotBoundarys }),
  placeId: "",
  setPlaceId: (placeId) => set({ placeId }),
}));
