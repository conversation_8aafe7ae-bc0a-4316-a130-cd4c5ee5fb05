import { create } from "zustand";

export type SceneStoreType = {
  darkIntensity: number;
  setDarkIntensity: (darkIntensity: number) => void;
  lightAdjustOpen: boolean;
  setLightAdjustOpen: (lightAdjustOpen: boolean) => void;
  isPartToggleOpen: boolean;
  setIsPartToggleOpen: (isPartToggleOpen: boolean) => void;
  hiddenParts: string[];
  setHiddenParts: (hiddenParts: string[]) => void;
  isFPVMode: boolean;
  setIsFPVMode: (isFPVMode: boolean) => void;
  isMenuOpen: boolean;
  setIsMenuOpen: (isMenuOpen: boolean) => void;
  isSettingOpen: boolean;
  setIsSettingOpen: (isSettingOpen: boolean) => void;
  isOrthographic: boolean;
  setIsOrthographic: (isOrthographic: boolean) => void;
  resetMultiViews: boolean;
  setResetMultiViews: (resetMultiViews: boolean) => void;
  multiViewsCycle: number;
  setMultiViewsCycle: (resetMultiViews: number) => void;
};

export const useSceneStore = create<SceneStoreType>((set) => ({
  darkIntensity: 0.35,
  setDarkIntensity: (darkIntensity) => set({ darkIntensity }),
  lightAdjustOpen: false,
  setLightAdjustOpen: (lightAdjustOpen) => set({ lightAdjustOpen }),
  isPartToggleOpen: false,
  setIsPartToggleOpen: (isPartToggleOpen) => set({ isPartToggleOpen }),
  hiddenParts: [],
  setHiddenParts: (hiddenParts) => set({ hiddenParts }),
  isFPVMode: false,
  setIsFPVMode: (isFPVMode) => set({ isFPVMode }),
  isMenuOpen: false,
  setIsMenuOpen: (isMenuOpen) => set({ isMenuOpen }),
  isSettingOpen: false,
  setIsSettingOpen: (isSettingOpen) => set({ isSettingOpen }),
  isOrthographic: false,
  setIsOrthographic: (isOrthographic) => set({ isOrthographic }),
  resetMultiViews: false,
  setResetMultiViews: (resetMultiViews) => set({ resetMultiViews }),
  multiViewsCycle: 0,
  setMultiViewsCycle: (multiViewsCycle) => set({ multiViewsCycle }),
}));
