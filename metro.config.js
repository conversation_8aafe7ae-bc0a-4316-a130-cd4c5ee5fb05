const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require("nativewind/metro");

const projectRoot = __dirname;

const config = getDefaultConfig(projectRoot);

config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  "js",
  "jsx",
  "json",
  "ts",
  "tsx",
  "cjs",
  "mjs",
];
config.resolver.assetExts = [...config.resolver.assetExts, "glb", "gltf", "png", "jpg", "hdr"];

module.exports = withNativeWind(config, { input: "./global.css" });
