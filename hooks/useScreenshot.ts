import * as THREE from "three";

export function useScreenshot(gl: THREE.WebGLRenderer) {
  async function takeScreenShot(filename: string, isAutoscreenshots: boolean) {
    return new Promise((resolve) => {
      gl.domElement.toBlob((blob) => {
        if (blob) {
          createPngFromBlob(blob, 1280, 720).then((value) => {
            const url = URL.createObjectURL(value);

            const link = document.createElement("a");
            link.href = url;
            link.download = filename;
            link.click();
            link.remove();
          });
          if (isAutoscreenshots)
            createPngFromBlob(blob, 400, 300).then((value) => {
              const url = URL.createObjectURL(value);

              const link = document.createElement("a");
              link.href = url;
              link.download = "small_" + filename;
              link.click();
            });
        } else {
          resolve(false);
        }
      });
    });
  }

  return {
    takeScreenShot,
  };
}

function createPngFromBlob(imageBlob: Blob, width: number, height: number): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();

    const blobUrl = URL.createObjectURL(imageBlob);
    img.src = blobUrl;

    img.onload = () => {
      let ratio = img.width / img.height;
      let desiredW = height * ratio;
      let desiredH = width / ratio;

      if (img.width > img.height) {
        desiredW = img.width * (width / img.width);
        desiredH = img.height * (width / img.width);
      } else {
        desiredH = img.height * (height / img.height);
        desiredW = img.width * (height / img.height);
      }

      const canvas = document.createElement("canvas");
      canvas.width = desiredW;
      canvas.height = desiredH;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(img, 0, 0, desiredW, desiredH);

        canvas.toBlob(
          (pngBlob) => {
            if (pngBlob) {
              resolve(pngBlob);
            } else {
              reject(new Error("Failed to create PNG blob."));
            }
          },
          "image/jpeg",
          80
        );
      } else {
        reject(new Error("Failed to get 2D rendering context."));
      }

      // 6. Revoke the object URL to free up memory
      URL.revokeObjectURL(blobUrl);
    };

    img.onerror = (error) => {
      URL.revokeObjectURL(blobUrl); // Revoke URL even on error
      reject(new Error(`Failed to load image from blob: ${error}`));
    };
  });
}
