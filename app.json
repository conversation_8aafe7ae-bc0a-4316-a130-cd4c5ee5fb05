{"expo": {"name": "Stramit3dVisualizer", "slug": "Stramit3dVisualizer", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "stramit3dvisualizer", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.marclu-stramit.Stramit3dVisualizer", "appleTeamId": "T3CCNDM5NM"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.marclustramit.Stramit3dVisualizer"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-web-browser", "expo-font"], "experiments": {"typedRoutes": true}}}