import Overlay from "@/components/hud/Overlay";
import Canvas3D from "@/components/scene/Canvas3D";
import { Box } from "@/components/ui/box";
import { useCarportStore } from "@/utils/zustand/carportStore";
import { useScreenShootsStore } from "@/utils/zustand/screenShootsStore";
import { useLocalSearchParams } from "expo-router";

export default function AutoScreenShoots() {
  const { model, auto } = useLocalSearchParams<{ model: string; auto: string }>();
  useCarportStore.setState({ modelId: model as string });

  async function waitforFirstRender(): Promise<string> {
    return new Promise((resolve) => {
      setTimeout(() => {
        useScreenShootsStore.setState({
          isAutoshoots: (auto == "true") as boolean,
        });
        resolve(" isAutoshoots set");
      }, 4000);
    });
  }
  waitforFirstRender();

  return (
    <Box className="flex-1">
      <Canvas3D />
      <Overlay />
    </Box>
  );
}
