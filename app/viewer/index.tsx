import Overlay from "@/components/hud/Overlay";
import Canvas3D from "@/components/scene/Canvas3D";
import { useCarportStore } from "@/utils/zustand/carportStore";
import { useLocalSearchParams } from "expo-router";
import { useEffect } from "react";
import { View } from "react-native";

export default function Index() {
  const { model } = useLocalSearchParams();
  const { setModelId } = useCarportStore();
  useEffect(() => {
    if (model) {
      setModelId(model as string);
    }
  }, [model, setModelId]);

  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      <Canvas3D />
      <Overlay />
    </View>
  );
}
