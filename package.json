{"name": "stramit3dvisualizer", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/toast": "^1.0.9", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-three/drei": "^10.3.0", "@react-three/fiber": "file:react-three-fiber-9.1.2.tgz", "@react-three/postprocessing": "^3.0.4", "axios": "^1.10.0", "expo": "53.0.15", "expo-blur": "^14.1.5", "expo-constants": "~17.1.6", "expo-font": "~13.3.2", "expo-gl": "~15.1.7", "expo-haptics": "~14.1.4", "expo-image": "~2.3.1", "expo-linking": "~7.1.6", "expo-router": "~5.1.2", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-three": "^8.0.0", "expo-web-browser": "~14.2.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-helmet": "^6.1.0", "react-native": "0.79.4", "react-native-aria": "^0.2.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-native-wgpu": "^0.2.0", "react-ui-animate": "^4.3.1", "tailwindcss": "^3.4.17", "three": "^0.177.0", "three-stdlib": "^2.36.0", "yarn": "^1.22.22", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@tailwindcss/container-queries": "^0.1.1", "@types/react": "~19.0.10", "@types/react-helmet": "^6", "@types/three": "^0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true, "packageManager": "yarn@4.9.2"}