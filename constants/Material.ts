import { CarportGroupType } from '@/types/carport';
import * as THREE from 'three';

export const MaterialsList: { [key in CarportGroupType]: THREE.MeshStandardMaterialParameters } = {
    [CarportGroupType.Post]: {
        color: 0xc0c0c0,
        metalness: 1,
        roughness: 0.2,
    },
    [CarportGroupType.Footing]: {
        color: 0x808080,
        metalness: 0.5,
        roughness: 0.8,
    },
    [CarportGroupType.Rafter]: {
        color: 0xb0c4de,
        metalness: 0.8,
        roughness: 0.4,
    },
    [CarportGroupType.Roof]: {
        color: 0x808080
    },
    [CarportGroupType.Slab]: {
        color: 0x808080,
        metalness: 0.5,
        roughness: 0.8,
    },
    [CarportGroupType.Mezzanine]: {
        color: 0x946d25,
        metalness: 0.5,
        roughness: 0.8,
    },
    [CarportGroupType.Wall]: {
        color: 0xD2B48C,
        metalness: 0.3,
        roughness: 0.7,
    },
    [CarportGroupType.BayWall]: {
        color: 0x000000,
        metalness: 0.3,
        roughness: 0.7,
    },
    [CarportGroupType.ApexBrace]: {
        color: 0xD2B48C,
        metalness: 0.3,
        roughness: 0.7,
    },
    [CarportGroupType.KneeBrace]: {
        color: 0xD2B48C,
        metalness: 0.3,
        roughness: 0.7,
    },
    [CarportGroupType.Flashing]: {
        color: 0xD2B48C,
        metalness: 0.3,
        roughness: 0.7,
    }
}

export function createMaterial(materialType: CarportGroupType): THREE.MeshStandardMaterial {
  return new THREE.MeshStandardMaterial({ ...MaterialsList[materialType] });
}
