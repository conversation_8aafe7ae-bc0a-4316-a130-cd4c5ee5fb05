export const colours = ['black', 'tan', 'silver', 'green', 'brown'] as const;
export type ColoursType = (typeof colours)[number];

export const coloursData: Record<
  ColoursType,
  {
    hex: string;
    rgb: `rgb(${number}, ${number}, ${number})`;
    rgbDecimal: string;
    name: string;
  }
> = {
  black: {
    hex: '#000000',
    rgb: 'rgb(0, 0, 0)',
    rgbDecimal: 'rgb(0.0000, 0.0000, 0.0000)',
    name: 'Black',
  },
  tan: {
    hex: '#D2B48C',
    rgb: 'rgb(210, 180, 140)',
    rgbDecimal: 'rgb(0.8235, 0.7058, 0.5490)',
    name: 'Tan',
  },
  silver: {
    hex: '#C0C0C0',
    rgb: 'rgb(192, 192, 192)',
    rgbDecimal: 'rgb(0.7529, 0.7529, 0.7529)',
    name: 'Silver',
  },
  green: {
    hex: '#008000',
    rgb: 'rgb(0, 128, 0)',
    rgbDecimal: 'rgb(0, 0.5020, 0)',
    name: 'Green',
  },
  brown: {
    hex: '#946d25',
    rgb: 'rgb(148, 109, 37)',
    rgbDecimal: 'rgb(0.5803, 0.4274, 0.145)',
    name: 'Brown',
  },
};
