import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { Gesture, GestureDetector, GestureHandlerRootView } from "react-native-gesture-handler";
import Animated, { useAnimatedStyle, useSharedValue } from "react-native-reanimated";

export function VerticalSlider({
  sliderWidth = 10,
  handleSize = 40,
  minValue = 0,
  maxValue = 100,
  step = 0.1,
  precision = 1,
  className = "",
  onChange = (value: number) => {},
}) {
  const offset = useSharedValue(0);
  const [sliderHeight, setSliderHeight] = useState(300);
  const maxOffset = sliderHeight - handleSize;

  const pan = Gesture.Pan().onChange((event) => {
    offset.value =
      Math.abs(offset.value) <= maxOffset
        ? offset.value + event.changeY <= 0
          ? 0
          : offset.value + event.changeY >= maxOffset
          ? maxOffset
          : offset.value + event.changeY
        : offset.value;
    const rawValue = (offset.value / maxOffset) * (maxValue - minValue) + minValue;
    const steppedValue = parseFloat((Math.round(rawValue / step) * step).toFixed(precision));
    onChange(steppedValue);
  });

  const sliderStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: offset.value }],
    };
  });

  return (
    <GestureHandlerRootView style={styles.container}>
      <View
        style={{ ...styles.sliderTrack, width: sliderWidth }}
        className={`rounded-full bg-white/60 ${className}`}
        onLayout={(event) => setSliderHeight(event.nativeEvent.layout.height)}
      >
        <GestureDetector gesture={pan}>
          <Animated.View
            className="rounded-full"
            style={[
              styles.sliderHandle,
              sliderStyle,
              {
                width: handleSize,
                height: handleSize,
                left: (sliderWidth - handleSize) / 2,
              },
            ]}
          />
        </GestureDetector>
      </View>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  sliderTrack: {
    height: "100%",
    justifyContent: "center",
  },
  sliderHandle: {
    backgroundColor: "#f8f9ff",
    position: "absolute",
    top: 0,
  },
});
