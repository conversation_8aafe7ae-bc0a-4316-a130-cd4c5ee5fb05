import { Vec2, Vec3 } from "../types";

export function sub3(a: Vec3, b: Vec3): Vec3 {
  "worklet";
  return { x: a.x - b.x, y: a.y - b.y, z: a.z - b.z };
}

export function sub2(a: Vec2, b: Vec2): Vec2 {
  "worklet";
  return { x: a.x - b.x, y: a.y - b.y };
}

export function sub(a: Vec3 | Vec2, b: Vec3 | Vec2): Vec3 | Vec2 {
  "worklet";
  if (typeof a === "object" && "z" in a && "z" in b) {
    return sub3(a, b);
  }
  return sub2(a, b);
}

export function distance(a: Vec3, b: Vec3): number {
  "worklet";
  return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2 + (a.z - b.z) ** 2);
}

export function normalize3(v: Vec3): Vec3 {
  "worklet";
  const length = Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
  if (length === 0) return { x: 0, y: 0, z: 0 };
  return {
    x: v.x / length,
    y: v.y / length,
    z: v.z / length,
  };
}

export function normalize2(v: Vec2): Vec2 {
  "worklet";
  const length = Math.sqrt(v.x * v.x + v.y * v.y);
  if (length === 0) return { x: 0, y: 0 };
  return { x: v.x / length, y: v.y / length };
}

export function normalize(v: Vec3 | Vec2): Vec3 | Vec2 {
  "worklet";
  if (typeof v === "object" && "z" in v) {
    return normalize3(v);
  }
  return normalize2(v);
}

export function cross3(a: Vec3, b: Vec3): Vec3 {
  "worklet";
  return {
    x: a.y * b.z - a.z * b.y,
    y: a.z * b.x - a.x * b.z,
    z: a.x * b.y - a.y * b.x,
  };
}

export function cross2(a: Vec2, b: Vec2): Vec2 {
  "worklet";
  return { x: a.y * b.y, y: a.x * b.x };
}

export function cross(a: Vec3 | Vec2, b: Vec3 | Vec2): Vec3 | Vec2 {
  "worklet";
  if (typeof a === "object" && "z" in a && "z" in b) {
    return cross3(a, b);
  }
  return cross2(a, b);
}

export function add3(a: Vec3, b: Vec3): Vec3 {
  "worklet";
  return { x: a.x + b.x, y: a.y + b.y, z: a.z + b.z };
}

export function add2(a: Vec2, b: Vec2): Vec2 {
  "worklet";
  return { x: a.x + b.x, y: a.y + b.y };
}

export function add(a: Vec3 | Vec2, b: Vec3 | Vec2): Vec3 | Vec2 {
  "worklet";
  if (typeof a === "object" && "z" in a && "z" in b) {
    return add3(a, b);
  }
  return add2(a, b);
}

export function scale3(v: Vec3, s: number): Vec3 {
  "worklet";
  return { x: v.x * s, y: v.y * s, z: v.z * s };
}

export function scale2(v: Vec2, s: number): Vec2 {
  "worklet";
  return { x: v.x * s, y: v.y * s };
}

export function scale(v: Vec3 | Vec2, s: number): Vec3 | Vec2 {
  "worklet";
  if (typeof v === "object" && "z" in v) {
    return scale3(v, s);
  }
  return scale2(v, s);
}

export function panLeft(d: number, target: Vec3, camera: Vec3) {
  "worklet";
  const vector = sub(camera, target);
  vector.y = 0;
  const forward = normalize(vector);
  const up = { x: 0, y: 1, z: 0 };
  const right = normalize(cross(forward, up));
  const offset = scale(right, d);

  return offset;
}

export function panForward(d: number, target: Vec3, camera: Vec3) {
  "worklet";
  const vector = sub(target, camera);
  vector.y = 0;
  const forward = normalize(vector);
  const offset = scale(forward, d);

  return offset;
}
