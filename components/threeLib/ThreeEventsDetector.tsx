import { Gesture, GestureDetector } from "react-native-gesture-handler";

import { View } from "react-native";

export default function ThreeEventsDetector({ children }: { children: React.ReactNode }) {
  const gesture = Gesture.Native();

  return (
    <GestureDetector gesture={gesture}>
      <View
        style={{
          flex: 1,
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
        pointerEvents="box-none"
      >
        {children}
      </View>
    </GestureDetector>
  );
}
