import { SharedValue, makeMutable } from "react-native-reanimated";

import { create } from "zustand";
import { Vec2, Vec3 } from "../types";

const EPS = 1e-6;
const DAMPING = 0.05;

const STATE = {
  NONE: -1,
  ROTATE: 0,
  DOLLY: 1,
  PAN: 2,
} as const;

type Object3D = {
  name: string;
  uuid: string;
  userData?: {
    bimType?: string;
  };
};

export type ThreeStoreType = {
  camera: SharedValue<Vec3>;
  target: SharedValue<Vec3>;
  moveStart: SharedValue<Vec3>;

  panStart: SharedValue<Vec2>;
  panDelta: SharedValue<Vec2>;

  canRotate: SharedValue<boolean>;
  canDolly: SharedValue<boolean>;
  canPan: SharedValue<boolean>;

  isDisabled: SharedValue<boolean>;
  maxPolarAngle: SharedValue<number>;
  minPolarAngle: SharedValue<number>;
  maxDistance: SharedValue<number>;
  minDistance: SharedValue<number>;

  dampingFactor: number;

  isTouching: SharedValue<boolean>;
  touchPosition: SharedValue<{ x: number; y: number; relative: boolean }>;
  touchingObject: SharedValue<Object3D>;
  draggingObject: SharedValue<Object3D>;
  draggingObjectPosition: SharedValue<Vec3>;

  cameraOffsets: SharedValue<Vec3[]>;
  targetOffsets: SharedValue<Vec3[]>;
  dollyDelta: SharedValue<{
    deltaX: number;
    deltaY: number;
  }>;

  isDirty: SharedValue<boolean>;
  twoFingerDistance: SharedValue<number>;
  dragStart: SharedValue<Vec2>;
  dragDelta: SharedValue<Vec2>;

  pressedKeys: SharedValue<string[]>;
};

const initialCamera: Vec3 = { x: -5, y: 5, z: 5 };
const initialTarget: Vec3 = { x: 0, y: 0, z: 0 };

export const useThreeInternalStore = create<ThreeStoreType>((set, get) => ({
  camera: makeMutable<Vec3>(initialCamera),
  target: makeMutable<Vec3>(initialTarget),
  moveStart: makeMutable<Vec3>(initialCamera),
  panStart: makeMutable<Vec2>({ x: 0, y: 0 }),
  panDelta: makeMutable<Vec2>({ x: 0, y: 0 }),

  canRotate: makeMutable<boolean>(true),
  canDolly: makeMutable<boolean>(true),
  canPan: makeMutable<boolean>(true),
  isDisabled: makeMutable<boolean>(false),
  maxPolarAngle: makeMutable<number>(Math.PI / 2 - 0.01),
  minPolarAngle: makeMutable<number>(0.1),
  maxDistance: makeMutable<number>(50),
  minDistance: makeMutable<number>(2),
  dampingFactor: DAMPING,

  isTouching: makeMutable<boolean>(false),
  touchPosition: makeMutable<{ x: number; y: number; relative: boolean }>({
    x: 0,
    y: 0,
    relative: false,
  }),
  touchingObject: makeMutable<Object3D>({ name: "", uuid: "" }),
  draggingObject: makeMutable<Object3D>({ name: "", uuid: "" }),
  draggingObjectPosition: makeMutable<Vec3>({ x: 0, y: 0, z: 0 }),

  isDirty: makeMutable<boolean>(true),
  dollyDelta: makeMutable<{
    deltaX: number;
    deltaY: number;
  }>({ deltaX: 0, deltaY: 0 }),
  cameraOffsets: makeMutable<Vec3[]>([]),
  targetOffsets: makeMutable<Vec3[]>([]),
  twoFingerDistance: makeMutable<number>(0),
  dragStart: makeMutable<Vec2>({ x: 0, y: 0 }),
  dragDelta: makeMutable<Vec2>({ x: 0, y: 0 }),
  pressedKeys: makeMutable<string[]>([]),
}));
