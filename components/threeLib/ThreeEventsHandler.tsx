import { invalidate } from "@react-three/fiber";
import { useAnimatedReaction } from "react-native-reanimated";
import * as THREE from "three";
import { runFrameCallbacks } from "./useFrameCallback";
import { useThreeInternalStore } from "./zustand/threeStore";

export default function ThreeEventsHandler() {
  const { isDirty } = useThreeInternalStore();

  useAnimatedReaction(
    () => {
      return isDirty.value;
    },
    () => {
      if (isDirty.value) {
        runFrameCallbacks();
        invalidate();
        isDirty.value = false;
      }
    }
  );

  return null;
}

const findDraggableRoot = (object: THREE.Object3D): THREE.Object3D | null => {
  let current = object;
  while (current) {
    if (current.userData.draggable) return current;
    current = current.parent!;
  }
  return null;
};
