import { useEffect, useRef } from "react";
import { View } from "react-native";
import { Gesture, GestureDetector, MouseButton } from "react-native-gesture-handler";
import { useWheel } from "react-ui-animate";
import { sub } from "./helpers/mathHelpers";
import { useThreeInternalStore } from "./zustand/threeStore";

export default function ThreeEventsDetector({ children }: { children: React.ReactNode }) {
  const {
    panStart,
    panDelta,
    isTouching,
    touchPosition,
    canRotate,
    draggingObject,
    isDirty,
    dollyDelta,
    twoFingerDistance,
    dragStart,
    dragDelta,
  } = useThreeInternalStore();

  const gestureViewRef = useRef<View>(null);

  const mobileDollyPanGesture = Gesture.Pinch()
    .onTouchesDown((event) => {
      if (event.allTouches.length === 2 && event.pointerType === 0) {
        const touch1 = { x: event.allTouches[0].x, y: event.allTouches[0].y };
        const touch2 = { x: event.allTouches[1].x, y: event.allTouches[1].y };
        const distance = Math.sqrt(
          Math.pow(touch1.x - touch2.x, 2) + Math.pow(touch1.y - touch2.y, 2)
        );
        twoFingerDistance.value = distance;

        const focusX = (touch1.x + touch2.x) / 2;
        const focusY = (touch1.y + touch2.y) / 2;
        panStart.value = { x: focusX, y: focusY };
        dollyDelta.value = { deltaX: 0, deltaY: 0 };
      }
    })
    .onTouchesMove((event) => {
      if (event.allTouches.length === 2 && event.pointerType === 0) {
        const touch1 = { x: event.allTouches[0].x, y: event.allTouches[0].y };
        const touch2 = { x: event.allTouches[1].x, y: event.allTouches[1].y };
        const distance = Math.sqrt(
          Math.pow(touch1.x - touch2.x, 2) + Math.pow(touch1.y - touch2.y, 2)
        );
        dollyDelta.value = { deltaX: 0, deltaY: (twoFingerDistance.value - distance) * 5 };
        twoFingerDistance.value = distance;

        const focusX = (touch1.x + touch2.x) / 2;
        const focusY = (touch1.y + touch2.y) / 2;
        panDelta.value = {
          x: (focusX - panStart.value.x) * 2,
          y: (focusY - panStart.value.y) * 2,
        };
        panStart.value = { x: focusX, y: focusY };
      }
    });

  const desktopPanGesture = Gesture.Pan()
    .mouseButton(MouseButton.RIGHT | MouseButton.MIDDLE)
    .onStart((event) => {
      panStart.value = { x: event.x, y: event.y };
      panDelta.value = { x: 0, y: 0 };
      touchPosition.value = {
        x: event.x,
        y: event.y,
        relative: true,
      };
    })
    .onUpdate((event) => {
      panDelta.value = {
        x: event.x - panStart.value.x,
        y: event.y - panStart.value.y,
      };
      panStart.value = { x: event.x, y: event.y };
    })
    .onEnd(() => {
      panDelta.value = { x: 0, y: 0 };
    });

  const dragGesture = Gesture.Pan()
    .minPointers(1)
    .maxPointers(1)
    .mouseButton(MouseButton.LEFT)
    .onStart((event) => {
      // isDirty.value = true;
      // if (draggingObject.value.uuid) {
      //   canRotate.value = false;
      //   const x = event.x;
      //   const y = event.y;
      //   panStart.value = { x, y };
      // } else {
      //   canRotate.value = true;
      //   draggingObject.value = { name: "", uuid: "" };
      // }
      if (canRotate.value) {
        dragStart.value = { x: event.x, y: event.y };
        dragDelta.value = { x: 0, y: 0 };
      }
    })
    .onUpdate((event) => {
      // isDirty.value = true;
      // if (draggingObject.value.uuid) {
      //   canRotate.value = false;
      //   const x = event.x;
      //   const y = event.y;
      //   panEnd.value = { x, y };
      //   panDelta.value = {
      //     x: (x - panStart.value.x) * 0.01,
      //     y: (y - panStart.value.y) * 0.01,
      //   };
      //   const d = distance(target.value, camera.value);
      //   const horizontalOffset = panLeft(panDelta.value.x, target.value, camera.value);
      //   const forwardOffset = panForward(panDelta.value.y, target.value, camera.value);
      //   const offset = scale(add(horizontalOffset, forwardOffset), d * 0.15);
      //   draggingObjectPosition.value = sub(draggingObjectPosition.value, offset) as Vec3;
      //   panStart.value = { x, y };
      // } else {
      //   canRotate.value = true;
      //   draggingObject.value = { name: "", uuid: "" };
      // }
      if (canRotate.value) {
        dragDelta.value = sub({ x: event.x, y: event.y }, dragStart.value);
        dragStart.value = { x: event.x, y: event.y };
      }
    })
    .onEnd(() => {
      canRotate.value = true;
      draggingObject.value = { name: "", uuid: "" };
    });

  const tapGesture = Gesture.Tap()
    .mouseButton(MouseButton.LEFT)
    .onBegin((event) => {
      isDirty.value = true;
      isTouching.value = true;
      touchPosition.value = {
        x: event.x,
        y: event.y,
        relative: true,
      };
    })
    .onStart((event) => {})
    .onEnd(() => {
      isDirty.value = true;
      draggingObject.value = { name: "", uuid: "" };
      touchPosition.value = {
        x: 0,
        y: 0,
        relative: true,
      };
    });

  const twoFinerGestures = Gesture.Simultaneous(desktopPanGesture, mobileDollyPanGesture);
  const oneFingerGesture = Gesture.Simultaneous(tapGesture, dragGesture);
  const gesture = Gesture.Race(twoFinerGestures, oneFingerGesture);

  const wheelGestureBinding = useWheel((event) => {
    if (event.isWheeling) {
      dollyDelta.value = { deltaX: event.deltaX, deltaY: event.deltaY };
    } else {
      dollyDelta.value = { deltaX: 0, deltaY: 0 };
    }
  });

  useEffect(() => {
    const target = gestureViewRef.current as unknown as HTMLElement;
    if (!target) return;

    const handleWheel = (event: WheelEvent) => {
      touchPosition.value = {
        x: event.clientX,
        y: event.clientY,
        relative: false,
      };
    };
    if (target) {
      target.addEventListener("wheel", handleWheel);
    }
    return () => {
      if (target) {
        target.removeEventListener("wheel", handleWheel);
      }
    };
  }, [gestureViewRef]);

  return (
    <GestureDetector gesture={gesture}>
      <View
        ref={gestureViewRef}
        style={{
          flex: 1,
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
        pointerEvents="box-none"
      >
        <View style={{ flex: 1 }} {...wheelGestureBinding()}>
          {children}
        </View>
      </View>
    </GestureDetector>
  );
}
