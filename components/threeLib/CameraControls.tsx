import { useThree } from "@react-three/fiber";
import { useEffect, useRef } from "react";
import { useAnimatedReaction } from "react-native-reanimated";
import { add, cross, distance, normalize, scale, sub } from "./helpers/mathHelpers";
import { Vec3 } from "./types";
import { useFrameCallback } from "./useFrameCallback";
import { useThreeInternalStore } from "./zustand/threeStore";

export default function CameraControls() {
  const {
    camera: cameraPosition,
    target: cameraTarget,
    isDirty,
    cameraOffsets,
    targetOffsets,
    minDistance,
    maxDistance,
    maxPolarAngle,
    minPolarAngle,
    pressedKeys,
  } = useThreeInternalStore();
  const { camera } = useThree();

  const requestRef = useRef<number>(0);
  const lastTimeRef = useRef<number>(performance.now());

  useEffect(() => {
    const loop = (currentTime: number) => {
      const delta = currentTime - lastTimeRef.current;
      lastTimeRef.current = currentTime;
      if (pressedKeys.value.length > 0) {
        const forward = normalize(sub(cameraTarget.value, camera.position)) as Vec3;
        const up = { x: 0, y: 1, z: 0 };
        const right = normalize(cross(forward, up)) as Vec3;
        const moveSpeed = 0.01;
        let offset = { x: 0, y: 0, z: 0 };

        const direction = pressedKeys.value.reduce(
          (acc: Vec3, key: string) => {
            if (key === "w" || key === "ArrowUp") {
              return add(acc, forward) as Vec3;
            }
            if (key === "s" || key === "ArrowDown") {
              return sub(acc, forward) as Vec3;
            }
            if (key === "a" || key === "ArrowLeft") {
              return sub(acc, right) as Vec3;
            }
            if (key === "d" || key === "ArrowRight") {
              return add(acc, right) as Vec3;
            }
            return acc;
          },
          { x: 0, y: 0, z: 0 } as Vec3
        );
        const normalizedDirection = normalize(direction) as Vec3;

        offset = add(offset, scale(normalizedDirection, moveSpeed * delta)) as Vec3;

        offset.y = 0;
        cameraOffsets.modify((v) => {
          "worklet";
          v.push(offset);
          return v;
        });
        targetOffsets.modify((v) => {
          "worklet";
          v.push(offset);
          return v;
        });
        isDirty.value = true;
      }
      requestRef.current = requestAnimationFrame(loop);
    };

    requestRef.current = requestAnimationFrame(loop);

    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, []);

  useFrameCallback(() => {
    // if (isTouching.value) {
    //   const { x: screenX, y: screenY } = touchPosition.value;
    //   const rect = { width: width.value, height: height.value };
    //   const x = (screenX / rect.width) * 2 - 1;
    //   const y = -(screenY / rect.height) * 2 + 1;

    //   raycaster.current.setFromCamera(new THREE.Vector2(x, y), camera);
    //   const intersects = raycaster.current.intersectObjects(scene.children, true);

    //   if (intersects.length > 0) {
    //     const hitObject = intersects[0].object;
    //     const draggableRoot = findDraggableRoot(hitObject);
    //     if (draggableRoot) {
    //       draggingObject.value = { name: draggableRoot.name, uuid: draggableRoot.uuid };

    //       draggingObjectPosition.value = {
    //         x: draggableRoot.position.x,
    //         y: draggableRoot.position.y,
    //         z: draggableRoot.position.z,
    //       };
    //     }
    //   }
    //   isTouching.value = false;
    // }
    // if (draggingObject.value.uuid) {
    //   const mesh = scene.getObjectByProperty("uuid", draggingObject.value.uuid) as THREE.Mesh;
    //   if (mesh) {
    //     mesh.position.set(
    //       draggingObjectPosition.value.x,
    //       draggingObjectPosition.value.y,
    //       draggingObjectPosition.value.z
    //     );
    //   }
    // }
    // if (cameraTarget.value && cameraPosition.value && !isDisabled.value) {
    //   camera.position.set(cameraPosition.value.x, cameraPosition.value.y, cameraPosition.value.z);
    //   camera.lookAt(cameraTarget.value.x, cameraTarget.value.y, cameraTarget.value.z);
    // }
    if (cameraOffsets.value.length > 0 || targetOffsets.value.length > 0) {
      const totalCameraOffset = cameraOffsets.value.reduce(
        (acc, offset) => {
          return add(acc, offset) as Vec3;
        },
        { x: 0, y: 0, z: 0 } as Vec3
      );
      cameraOffsets.value = [];
      const nextCamera = add(camera.position, totalCameraOffset) as Vec3;

      const totalTargetOffset = targetOffsets.value.reduce(
        (acc, offset) => {
          return add(acc, offset) as Vec3;
        },
        { x: 0, y: 0, z: 0 } as Vec3
      );
      targetOffsets.value = [];
      const nextTarget = add(cameraTarget.value, totalTargetOffset) as Vec3;
      const nextDistance = distance(nextCamera, nextTarget);
      const phi = Math.acos(nextCamera.y / nextDistance);
      if (
        nextDistance >= minDistance.value &&
        nextDistance <= maxDistance.value &&
        phi > minPolarAngle.value &&
        phi < maxPolarAngle.value
      ) {
        camera.position.set(nextCamera.x, nextCamera.y, nextCamera.z);
        camera.lookAt(nextTarget.x, nextTarget.y, nextTarget.z);
        cameraTarget.value = nextTarget;
      }
    }
  });

  useAnimatedReaction(
    () => {
      return cameraPosition.value;
    },
    (position) => {
      camera.position.set(position.x, position.y, position.z);
    }
  );

  useAnimatedReaction(
    () => {
      return cameraTarget.value;
    },
    (target) => {
      camera.lookAt(target.x, target.y, target.z);
    }
  );

  return null;
}
