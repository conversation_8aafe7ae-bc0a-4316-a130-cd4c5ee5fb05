import { getModelSize } from "@/utils/utils";
import { useSceneStore } from "@/utils/zustand/sceneStore";
import { invalidate, useFrame, useThree } from "@react-three/fiber";
import { useMemo } from "react";
import { useAnimatedReaction, useSharedValue } from "react-native-reanimated";
import * as THREE from "three";
import { runFrameCallbacks } from "./useFrameCallback";
import { useThreeInternalStore } from "./zustand/threeStore";

type Viewport = {
  key: string;
  position: [number, number, number];
  size: number;
  lookAt: [number, number, number];
  rect: [number, number, number, number];
};

export default function MultiViewsControls() {
  const { isDirty, panDelta, dollyDelta, touchPosition } = useThreeInternalStore();
  const { resetMultiViews, setResetMultiViews, multiViewsCycle, setMultiViewsCycle } =
    useSceneStore();
  const { scene, gl, size } = useThree();
  const boundingBoxSize = getModelSize();
  const initialValue: Viewport[] = [
    {
      key: "top View",
      position: [0 + boundingBoxSize.x / 2, 20, 0 - boundingBoxSize.z / 2],
      size: 10,
      lookAt: [0 + boundingBoxSize.x / 2, 0, 0 + 0 - boundingBoxSize.z / 2],
      rect: [0 / 3, 1 / 2, 1 / 3, 1 / 2],
    },
    {
      key: "Front View",
      position: [0 + boundingBoxSize.x / 2, 0, -10],
      size: 10,
      lookAt: [0 + boundingBoxSize.x / 2, 0, 0],
      rect: [1 / 3, 1 / 2, 1 / 3, 1 / 2],
    },
    {
      key: "Ortho A View",
      position: [-10 + boundingBoxSize.x / 2, 10, -10 - boundingBoxSize.z / 2],
      size: 10,
      lookAt: [0 + boundingBoxSize.x / 2, 0, 0 - boundingBoxSize.z / 2],
      rect: [2 / 3, 1 / 2, 1 / 3, 1 / 2],
    },
    {
      key: "Rgiht View",
      position: [-10 + boundingBoxSize.x / 2, 0, 0 - boundingBoxSize.z / 2],
      size: 10,
      lookAt: [0 + boundingBoxSize.x / 2, 0, 0 - boundingBoxSize.z / 2],
      rect: [0 / 3, 0 / 2, 1 / 3, 1 / 2],
    },
    {
      key: "Left View",
      position: [10 + boundingBoxSize.x / 2, 0, 0 - boundingBoxSize.z / 2],
      size: 10,
      lookAt: [0 + boundingBoxSize.x / 2, 0, 0 - boundingBoxSize.z / 2],
      rect: [1 / 3, 0 / 2, 1 / 3, 1 / 2],
    },
    {
      key: "Ortho B View",
      position: [10 + boundingBoxSize.x / 2, 10, 10 - boundingBoxSize.z / 2],
      size: 10,
      lookAt: [0 + boundingBoxSize.x / 2, 0, 0 - boundingBoxSize.z / 2],
      rect: [2 / 3, 0 / 2, 1 / 3, 1 / 2],
    },
  ];

  const savedInitalValue = initialValue;
  const viewports = useSharedValue<Viewport[]>(initialValue);

  const cameras = useMemo(() => {
    return viewports.value.map(({ position, lookAt }) => {
      const cam = new THREE.OrthographicCamera(1, 1, 1, 1, -1000, 1000);
      cam.position.set(position[0], position[1], position[2]);
      cam.lookAt(lookAt[0], lookAt[1], lookAt[2]);
      return cam;
    });
  }, []);

  useAnimatedReaction(
    () => {
      return {
        panDelta: panDelta.value,
        dollyDelta: dollyDelta.value,
        touchPosition: touchPosition.value,
        resetMultiViews: resetMultiViews,
        multiViewsCycle: multiViewsCycle,
      };
    },
    ({ panDelta, dollyDelta, touchPosition, resetMultiViews, multiViewsCycle }) => {
      let { x: touchX, y: touchY } = touchPosition;

      if (!touchPosition.relative) {
        touchX = touchX - size.left;
        touchY = touchY - size.top;
      }
      const clientX = touchX / size.width;
      const clientY = (size.height - touchY) / size.height;
      const viewport = viewports.value.find((view) => {
        const [x, y, w, h] = view.rect;
        return clientX >= x && clientX <= x + w && clientY >= y && clientY <= y + h;
      });
      if (viewport) {
        const index = viewports.value.findIndex((view) => view.key === viewport.key);
        const newSize = viewports.value[index].size + dollyDelta.deltaY * 0.05;
        if (newSize > 0.5 && newSize < 300) {
          viewports.modify((value) => {
            value[index].size = newSize;
            return value;
          });
        }

        const forward = {
          x: viewport.lookAt[0] - viewport.position[0],
          y: viewport.lookAt[1] - viewport.position[1],
          z: viewport.lookAt[2] - viewport.position[2],
        };
        const forwardLength = Math.sqrt(
          forward.x * forward.x + forward.y * forward.y + forward.z * forward.z
        );
        const normalizedForward = {
          x: forward.x / forwardLength,
          y: forward.y / forwardLength,
          z: forward.z / forwardLength,
        };

        let up = { x: 0, y: 1, z: 0 };
        if (index === 0) {
          up = { x: 0, y: 0, z: -1 };
        }
        const left = {
          x: up.y * normalizedForward.z - up.z * normalizedForward.y,
          y: up.z * normalizedForward.x - up.x * normalizedForward.z,
          z: up.x * normalizedForward.y - up.y * normalizedForward.x,
        };
        const leftOffset = {
          x: left.x * panDelta.x * 0.05,
          y: left.y * panDelta.x * 0.05,
          z: left.z * panDelta.x * 0.05,
        };
        const upOffset = {
          x: up.x * panDelta.y * 0.05,
          y: up.y * panDelta.y * 0.05,
          z: up.z * panDelta.y * 0.05,
        };

        const newLookAt = {
          x: viewports.value[index].lookAt[0] + leftOffset.x + upOffset.x,
          y: viewports.value[index].lookAt[1] + leftOffset.y + upOffset.y,
          z: viewports.value[index].lookAt[2] + leftOffset.z + upOffset.z,
        };
        const newPosition = {
          x: viewport.position[0] + leftOffset.x + upOffset.x,
          y: viewport.position[1] + leftOffset.y + upOffset.y,
          z: viewport.position[2] + leftOffset.z + upOffset.z,
        };
        viewports.modify((value) => {
          value[index].lookAt = [newLookAt.x, newLookAt.y, newLookAt.z];
          value[index].position = [newPosition.x, newPosition.y, newPosition.z];
          return value;
        });

        if (resetMultiViews) {
          savedInitalValue.forEach((view, index) => {
            viewports.modify((value) => {
              value[index].lookAt = view.lookAt;
              value[index].position = view.position;
              value[index].size = view.size;
              value[index].rect = view.rect;
              return value;
            });
          });
          setMultiViewsCycle(0);
          setResetMultiViews(false);
        } else if (multiViewsCycle > 6) {
          setResetMultiViews(true);
        } else if (multiViewsCycle >= 1 && multiViewsCycle <= 6) {
          savedInitalValue.forEach((view, index) => {
            viewports.modify((value) => {
              if (index == multiViewsCycle - 1) {
                value[index].rect = [0, 0, 1, 1];
              } else {
                value[index].rect = [0, 0, 0, 0];
              }
              return value;
            });
          });
        }

        isDirty.value = true;
      }
    }
  );

  useFrame(() => {
    gl.setScissorTest(true);

    viewports.value.forEach((view, i) => {
      const [x, y, w, h] = view.rect;
      const leftPx = x * size.width;
      const bottomPx = y * size.height;
      const widthPx = w * size.width;
      const heightPx = h * size.height;

      const aspect = widthPx / heightPx;
      const camSize = view.size - 3; // I do not fully understand marc code but the -3 give us more zoom in.

      const camera = cameras[i] as THREE.OrthographicCamera;
      camera.position.set(view.position[0], view.position[1], view.position[2]);
      camera.lookAt(view.lookAt[0], view.lookAt[1], view.lookAt[2]);
      camera.left = -camSize * aspect;
      camera.right = camSize * aspect;
      camera.top = camSize;
      camera.bottom = -camSize;
      camera.updateProjectionMatrix();

      gl.setViewport(leftPx, bottomPx, widthPx, heightPx);
      gl.setScissor(leftPx, bottomPx, widthPx, heightPx);

      gl.setClearColor(0xccd3de, 1);
      gl.clearDepth();
      gl.clear(true);

      gl.render(scene, camera);
    });

    gl.setScissorTest(false);
  }, 1);

  useAnimatedReaction(
    () => {
      return isDirty.value;
    },
    () => {
      if (isDirty.value) {
        runFrameCallbacks();
        invalidate();
        isDirty.value = false;
      }
    }
  );

  return null;
}
