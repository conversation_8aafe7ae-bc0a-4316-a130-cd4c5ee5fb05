import { useEffect, useState } from "react";
import { Image, Platform } from "react-native";
import * as THREE from "three";
// import { DRACOLoader, G<PERSON>FLoader, RGBELoader } from "three-stdlib";
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader.js";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { RGBELoader } from "three/examples/jsm/loaders/RGBELoader.js";

export interface GLTF {
  animations: THREE.AnimationClip[];
  scene: THREE.Group;
  scenes: THREE.Group[];
  cameras: THREE.Camera[];
  asset: {
    copyright?: string | undefined;
    generator?: string | undefined;
    version?: string | undefined;
    minVersion?: string | undefined;
    extensions?: any;
    extras?: any;
  };
  parser: any;
  userData: Record<string, any>;
}

export const resolveAsset = (mod: ReturnType<typeof require>) => {
  if (Platform.OS === "web") {
    // For web, just return the static path
    return mod;
  } else {
    // For native (iOS/Android), use Image.resolveAssetSource
    return Image.resolveAssetSource(mod).uri;
  }
};

export const debugManager = new THREE.LoadingManager();

debugManager.onStart = function (url, itemsLoaded, itemsTotal) {
  console.log(
    "Started loading file: " + url + ".\nLoaded " + itemsLoaded + " of " + itemsTotal + " files."
  );
};

debugManager.onProgress = function (url, itemsLoaded, itemsTotal) {
  console.log(
    "Loading file: " + url + ".\nLoaded " + itemsLoaded + " of " + itemsTotal + " files."
  );
};

debugManager.onError = function (url) {
  console.error("There was an error loading " + url);
};

// export const useGeometry = (uri: string) => {
//   const [geometry, setGeometry] = useState<THREE.BufferGeometry | null>(null);
//   useEffect(() => {
//     const loader = new THREE.BufferGeometryLoader();
//     loader.load(uri, function (geo) {
//       setGeometry(geo);
//     });
//   }, [uri]);
//   return geometry;
// };

export const useRGBE = (asset: ReturnType<typeof require>) => {
  const url = resolveAsset(asset);
  const [texture, setTexture] = useState<THREE.Texture | null>(null);
  useEffect(() => {
    const loader = new RGBELoader();
    loader.load(url as string, function (tex: THREE.Texture) {
      setTexture(tex);
    });
  }, [url]);
  return texture;
};

export const useGLTF = (asset: ReturnType<typeof require> | string) => {
  const [GLTF, setGLTF] = useState<GLTF | null>(null);
  useEffect(() => {
    const loader = new GLTFLoader(debugManager);
    const dracoLoader = new DRACOLoader();
    loader.setDRACOLoader(dracoLoader);
    loader.load(asset as string, (model: GLTF) => {
      setGLTF(model);
    });

    return () => {
      dracoLoader.dispose();
    };
  }, [asset]);
  return GLTF;
};

// export const useTexture = (asset: ReturnType<typeof require> | string) => {
//   const [texture, setTexture] = useState<THREE.Texture | null>(null);

//   useEffect(() => {
//     const loadTexture = async () => {
//       try {
//         const uri = typeof asset === "string"
//           ? asset
//           : Asset.fromModule(asset).uri;

//         const loader = new THREE.TextureLoader();
//         loader.load(
//           uri,
//           (tex) => {
//             tex.needsUpdate = true;
//             setTexture(tex);
//           },
//           undefined,
//           (err) => {
//             console.error("Failed to load texture", err);
//           }
//         );
//       } catch (err) {
//         console.error("Error in useTexture:", err);
//       }
//     };

//     loadTexture();
//   }, [asset]);

//   return texture;
// };
