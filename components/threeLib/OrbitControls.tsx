import { useThree } from "@react-three/fiber";
import { useEffect } from "react";
import { useAnimatedReaction } from "react-native-reanimated";
import { add, distance, panForward, panLeft, scale, sub } from "./helpers/mathHelpers";
import { Vec3 } from "./types";
import { useThreeInternalStore } from "./zustand/threeStore";

export default function OrbitControls({
  maxPolarAngle: maxPolarAngleProp = Math.PI / 2 - 0.01,
  minPolarAngle: minPolarAngleProp = 0.1,
  maxDistance: maxDistanceProp = 50,
  minDistance: minDistanceProp = 2,
}: {
  maxPolarAngle?: number;
  minPolarAngle?: number;
  maxDistance?: number;
  minDistance?: number;
}) {
  const {
    target,
    panDelta,
    dollyDelta,
    isDirty,
    cameraOffsets,
    targetOffsets,
    dragDelta,
    maxPolarAngle,
    minPolarAngle,
    maxDistance,
    minDistance,
  } = useThreeInternalStore();

  useEffect(() => {
    maxPolarAngle.value = maxPolarAngleProp;
    minPolarAngle.value = minPolarAngleProp;
    maxDistance.value = maxDistanceProp;
    minDistance.value = minDistanceProp;
  }, [
    maxPolarAngleProp,
    minPolarAngleProp,
    maxPolarAngle,
    minPolarAngle,
    maxDistanceProp,
    minDistanceProp,
    maxDistance,
    minDistance,
  ]);

  const { camera } = useThree();

  // listen to wheel delta and add it to camera offsets
  useAnimatedReaction(
    () => {
      return dollyDelta.value;
    },
    (dollyDelta) => {
      const forward = sub(camera.position, target.value);
      const offset = scale(forward, dollyDelta.deltaY * 0.001) as Vec3;

      cameraOffsets.modify((v) => {
        "worklet";
        v.push(offset);
        return v;
      });
      isDirty.value = true;
    }
  );

  // listen to pan delta and add it to camera/target offsets
  useAnimatedReaction(
    () => {
      return panDelta.value;
    },
    (panDelta) => {
      const d = distance(target.value, camera.position);
      const horizontalOffset = panLeft(panDelta.x * 0.001, target.value, camera.position);
      const forwardOffset = panForward(panDelta.y * 0.001, target.value, camera.position);
      const offset = scale(add(horizontalOffset, forwardOffset), d) as Vec3;

      cameraOffsets.modify((v) => {
        "worklet";
        v.push(offset);
        return v;
      });
      targetOffsets.modify((v) => {
        "worklet";
        v.push(offset);
        return v;
      });
      isDirty.value = true;
    }
  );

  useAnimatedReaction(
    () => {
      return dragDelta.value;
    },
    (dragDelta) => {
      const rotateSpeed = 0.0005;

      const rotateDelta = scale(dragDelta, rotateSpeed);
      const thetaDelta = 2 * Math.PI * rotateDelta.x;
      const phiDelta = 2 * Math.PI * rotateDelta.y;
      const radius = distance(target.value, camera.position);

      const dx = camera.position.x - target.value.x;
      const dy = camera.position.y - target.value.y;
      const dz = camera.position.z - target.value.z;
      const theta = Math.atan2(dx, dz);
      const phi = Math.acos(dy / radius);

      const sphericalDelta = {
        radius,
        theta: theta - thetaDelta,
        phi: phi - phiDelta,
      };

      const sinPhiRadius = Math.sin(sphericalDelta.phi) * radius;
      const x = target.value.x + sinPhiRadius * Math.sin(sphericalDelta.theta);
      const y = target.value.y + Math.cos(sphericalDelta.phi) * radius;
      const z = target.value.z + sinPhiRadius * Math.cos(sphericalDelta.theta);
      const offset = {
        x: x - camera.position.x,
        y: y - camera.position.y,
        z: z - camera.position.z,
      };
      cameraOffsets.modify((v) => {
        "worklet";
        v.push(offset);
        return v;
      });

      isDirty.value = true;
    }
  );

  return null;
}
