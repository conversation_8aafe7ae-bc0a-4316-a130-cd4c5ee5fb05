import { useEffect } from "react";
type FrameCallback = () => void;
const frameCallbacks = new Set<FrameCallback>();

export function addFrameCallback(fn: FrameCallback) {
  frameCallbacks.add(fn);
}

export function removeFrameCallback(fn: FrameCallback) {
  frameCallbacks.delete(fn);
}

export function runFrameCallbacks() {
  frameCallbacks.forEach((fn) => fn());
}

export function useFrameCallback(callback: FrameCallback) {
  useEffect(() => {
    addFrameCallback(callback);
    return () => {
      removeFrameCallback(callback);
    };
  }, [callback]);
}
