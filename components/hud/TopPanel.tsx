import { Box } from "@/components/ui/box";
import { Pressable } from "@/components/ui/pressable";
import { useMapStore } from "@/utils/zustand/mapStore";
import { useSceneStore } from "@/utils/zustand/sceneStore";
import AntDesign from "@expo/vector-icons/AntDesign";
import Ionicons from "@expo/vector-icons/Ionicons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import PlaceAutoComplete from "./PlaceAutoComplete";

export default function TopPanel() {
  const { isMapOpen, setIsMapOpen } = useMapStore();
  const { isPartToggleOpen, setIsPartToggleOpen, isMenuOpen, setIsMenuOpen } = useSceneStore();
  return (
    <Box
      className="absolute bg-black/20 border border-gray-200/20 top-2 left-2 right-2 gap-2 flex-row items-center rounded-full pointer-events-auto backdrop-blur-md shadow-md shadow-black/10
    md:bg-transparent md:border-none md:shadow-none md:backdrop-blur-none md:pointer-events-none
    justify-between
    "
    >
      <Box className="flex-row gap-2 md:backdrop-blur-md md:bg-black/20 md:border md:border-gray-200/20 md:rounded-full p-2 md:pointer-events-auto">
        {isMenuOpen ? (
          <Pressable
            className="rounded-full bg-gray-200 h-[38px] w-[38px] items-center justify-center"
            onPress={() => setIsMenuOpen(false)}
          >
            <AntDesign name="close" size={24} color="black" />
          </Pressable>
        ) : (
          <Pressable
            className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center"
            onPress={() => setIsMenuOpen(true)}
          >
            <MaterialIcons name="menu" size={24} color="white" />
          </Pressable>
        )}
        <Pressable className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center">
          <AntDesign name="sharealt" size={24} color="white" />
        </Pressable>
      </Box>
      <Box className="justify-end flex-row items-center gap-2 md:backdrop-blur-md md:bg-black/20 md:border md:border-gray-200/20 md:rounded-full p-2 md:pointer-events-auto">
        {isMapOpen && <PlaceAutoComplete />}
        {isMapOpen ? (
          <Pressable
            className="rounded-full bg-gray-200 h-[38px] w-[38px] items-center justify-center"
            onPress={() => setIsMapOpen(false)}
          >
            <AntDesign name="close" size={24} color="black" />
          </Pressable>
        ) : (
          <Pressable
            className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center"
            onPress={() => setIsMapOpen(true)}
          >
            <Ionicons name="location-outline" size={24} color="white" />
          </Pressable>
        )}

        {isPartToggleOpen ? (
          <Pressable
            className="rounded-full bg-gray-200 h-[38px] w-[38px] items-center justify-center"
            onPress={() => setIsPartToggleOpen(false)}
          >
            <AntDesign name="caretup" size={24} color="black" className="top-[1px]" />
          </Pressable>
        ) : (
          <Pressable
            className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center"
            onPress={() => setIsPartToggleOpen(true)}
          >
            <AntDesign name="caretdown" size={24} color="white" className="top-[-1px]" />
          </Pressable>
        )}
      </Box>
    </Box>
  );
}
