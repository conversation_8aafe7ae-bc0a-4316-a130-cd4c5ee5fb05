import { Box } from "@/components/ui/box";
import { Text } from "@/components/ui/text/";
import { useSceneStore } from "@/utils/zustand/sceneStore";

export default function OrthographicTitles(Props: any) {
  const { resetMultiViews, setResetMultiViews, multiViewsCycle, setMultiViewsCycle } =
    useSceneStore();

  const TextStyle = {
    position: "absolute",
    fontSize: "30px",
    color: "#808080",
    top: "5%",
    left: "16%",
    transform: "translate(-50%, -50%)",
  };

  return (
    <Box className={` absolute top-0 left-0 w-full h-full  `}>
      <Text
        style={{
          ...TextStyle,
          top: multiViewsCycle == 0 ? "5%" : "10%",
          left: multiViewsCycle == 0 ? "16%" : "50%",
          display: multiViewsCycle == 0 || multiViewsCycle == 1 ? "block" : "none",
        }}
      >
        Top View
      </Text>

      <Text
        style={{
          ...TextStyle,

          top: multiViewsCycle == 0 ? "5%" : "10%",
          left: multiViewsCycle == 2 ? "50%" : "50%",
          display: multiViewsCycle == 0 || multiViewsCycle == 2 ? "block" : "none",
        }}
      >
        Front View
      </Text>

      <Text
        style={{
          ...TextStyle,

          top: multiViewsCycle == 0 ? "45%" : "10%",
          left: multiViewsCycle == 3 ? "50%" : "85%",
          display: multiViewsCycle == 0 || multiViewsCycle == 3 ? "block" : "none",
        }}
      >
        Ortho A View
      </Text>

      <Text
        style={{
          ...TextStyle,

          top: multiViewsCycle == 0 ? "45%" : "10%",
          left: multiViewsCycle == 4 ? "50%" : "16%",
          display: multiViewsCycle == 0 || multiViewsCycle == 4 ? "block" : "none",
        }}
      >
        Right View
      </Text>

      <Text
        style={{
          ...TextStyle,

          top: multiViewsCycle == 0 ? "45%" : "10%",
          left: multiViewsCycle == 5 ? "50%" : "50%",
          display: multiViewsCycle == 0 || multiViewsCycle == 5 ? "block" : "none",
        }}
      >
        Left View
      </Text>

      <Text
        style={{
          ...TextStyle,
          top: multiViewsCycle == 0 ? "5%" : "10%",
          left: multiViewsCycle == 6 ? "50%" : "85%",
          display: multiViewsCycle == 0 || multiViewsCycle == 6 ? "block" : "none",
        }}
      >
        Ortho B View
      </Text>
    </Box>
  );
}
