import { Box } from "@/components/ui/box";
import { Checkbox, CheckboxIcon, CheckboxIndicator, CheckboxLabel } from "@/components/ui/checkbox";
import { CheckIcon } from "@/components/ui/icon";
import { Text } from "@/components/ui/text";
import { useSceneStore } from "@/utils/zustand/sceneStore";

export const hideItemList = [
  { key: "cladding", label: "Cladding" },
  { key: "skylight", label: "Skylight / Roof Vents" },
  { key: "flashing", label: "Flashing" },
  { key: "slab", label: "Slab / Ground" },
  { key: "foot", label: "Footing" },
  { key: "column", label: "Column" },
  { key: "brace", label: "Strap Bracing" },
  { key: "stairs", label: "Stairs" },
  { key: "bracket", label: "Brackets" },
];

export default function PartTogglePanel() {
  const { hiddenParts, setHiddenParts } = useSceneStore();
  return (
    <Box className="absolute top-[66px] right-2 bg-black/20 backdrop-blur-md border border-gray-200/20 rounded-[28px] p-4 pointer-events-auto flex-col gap-2">
      <Box className="gap-2">
        <Text className="text-white">Select parts to hide</Text>
        {hideItemList.map((item) => (
          <Checkbox
            key={item.key}
            size="md"
            isInvalid={false}
            isDisabled={false}
            value={item.key}
            isChecked={hiddenParts.includes(item.key)}
            onChange={(isChecked) => {
              if (isChecked) {
                const newHiddenParts = [...hiddenParts, item.key];
                setHiddenParts(newHiddenParts);
              } else {
                const newHiddenParts = hiddenParts.filter((key) => key !== item.key);
                setHiddenParts(newHiddenParts);
              }
            }}
          >
            <CheckboxIndicator className="border-gray-200/80">
              <CheckboxIcon as={CheckIcon} />
            </CheckboxIndicator>
            <CheckboxLabel className="text-white">{item.label}</CheckboxLabel>
          </Checkbox>
        ))}
      </Box>
    </Box>
  );
}
