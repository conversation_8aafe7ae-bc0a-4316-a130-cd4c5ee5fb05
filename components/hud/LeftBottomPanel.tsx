import { Box } from "@/components/ui/box";
import { Pressable } from "@/components/ui/pressable";
import { useSceneStore } from "@/utils/zustand/sceneStore";
import AntDesign from "@expo/vector-icons/AntDesign";
import Ionicons from "@expo/vector-icons/Ionicons";

export default function LeftBottomPanel() {
  const { lightAdjustOpen, setLightAdjustOpen } = useSceneStore();
  return (
    <Box className="absolute bg-black/20 border p-2 border-gray-200/20 bottom-2 left-2 gap-2 flex-row items-center rounded-full pointer-events-auto backdrop-blur-md shadow-md shadow-black/10">
      <Pressable className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center">
        <AntDesign name="setting" size={24} color="white" />
      </Pressable>

      {lightAdjustOpen ? (
        <Pressable
          className="rounded-full bg-gray-200 h-[38px] w-[38px] items-center justify-center"
          onPress={() => setLightAdjustOpen(false)}
        >
          <AntDesign name="close" size={24} color="black" />
        </Pressable>
      ) : (
        <Pressable
          className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center"
          onPress={() => setLightAdjustOpen(true)}
        >
          <Ionicons name="sunny-outline" size={24} color="white" />
        </Pressable>
      )}
    </Box>
  );
}