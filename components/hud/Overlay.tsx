import { Box } from "@/components/ui/box";
import { Pressable } from "@/components/ui/pressable";
import { Slider, SliderFilledTrack, SliderThumb, SliderTrack } from "@/components/ui/slider";
import { Text } from "@/components/ui/text";

import { getPlaceDetails } from "@/utils/api/mapRequest";
import { useMapStore } from "@/utils/zustand/mapStore";
import { useSceneStore } from "@/utils/zustand/sceneStore";
import { useScreenShootsStore } from "@/utils/zustand/screenShootsStore";
import Ionicons from "@expo/vector-icons/Ionicons";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { useState } from "react";
import { Platform } from "react-native";
import { useShallow } from "zustand/react/shallow";
import LeftBottomPanel from "./LeftBottomPanel";
import OrthographicTitles from "./OrthographicTitles";
import PartTogglePanel from "./PartTogglePanel";
import TopPanel from "./TopPanel";

export default function Overlay() {
  const {
    places,
    search,
    setCoordinates,
    isMapOpen,
    coordinates,
    setSearch,
    setPlaces,
    setPlaceId,
  } = useMapStore();
  const {
    darkIntensity,
    setDarkIntensity,
    lightAdjustOpen,
    isPartToggleOpen,
    isMenuOpen,
    isFPVMode,
    setIsFPVMode,
    isOrthographic,
    setIsOrthographic,

    resetMultiViews,
    setResetMultiViews,
    multiViewsCycle,
    setMultiViewsCycle,
  } = useSceneStore(useShallow((s) => s));

  const { setIsTakeScreenShot } = useScreenShootsStore();

  const [isMenuHover, setIsMenuHover] = useState(false);

  function handlePlacePress(place: any) {
    setSearch(place.description);
    setPlaces([]);
    getPlaceDetails(place.place_id, "en").then((res) => {
      if (res.success && res.result) {
        if (res.result?.geometry?.location) {
          if (
            coordinates.lat === res.result.geometry.location.lat &&
            coordinates.lng === res.result.geometry.location.lng
          ) {
            return;
          }
          setCoordinates({
            lat: res.result.geometry.location.lat,
            lng: res.result.geometry.location.lng,
          });
          setPlaceId(place.place_id);
          console.log("placeId", place.place_id);
        }
      }
    });
  }

  return (
    <Box
      className={`flex-1 absolute top-0 left-0 w-full h-full ${
        Platform.OS === "web" ? "pointer-events-none" : "pointer-events-auto"
      }`}
    >
      <TopPanel />
      {places.length > 0 && search && isMapOpen && (
        <Box
          className="border border-gray-200/20 rounded-[20px] p-3 gap-3 absolute top-[68px] backdrop-blur-md shadow-md shadow-black/10
          right-2 left-2 sm:right-[108px] sm:w-[400px] sm:left-auto bg-black/20 pointer-events-auto"
        >
          {places.map((place) => (
            <Pressable key={place.place_id} onPress={() => handlePlacePress(place)}>
              <Text size="sm" className="no-wrap overflow-hidden text-ellipsis text-white">
                {place.description}
              </Text>
            </Pressable>
          ))}
        </Box>
      )}
      {lightAdjustOpen && (
        <Box className="absolute bottom-[68px] left-2 top-[68px] items-center justify-center">
          <Box
            className="border border-gray-200/20 rounded-full p-2 gap-2 backdrop-blur-md shadow-md shadow-black/10
           bg-black/20 pointer-events-auto max-h-[400px] h-full"
          >
            <Pressable
              className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center"
              onPress={() => {
                setDarkIntensity(0.3);
              }}
            >
              <MaterialCommunityIcons
                name="weather-sunny"
                size={26}
                color="rgba(255,255,255,0.9)"
              />
            </Pressable>
            <Box className="h-full py-2">
              <Slider
                value={darkIntensity}
                onChange={(value) => setDarkIntensity(value as number)}
                size="md"
                orientation="vertical"
                isReversed={true}
                minValue={0}
                maxValue={1}
                step={0.01}
              >
                <SliderTrack>
                  <SliderFilledTrack />
                </SliderTrack>
                <SliderThumb className="bg-gray-200 data-[active=true]:bg-gray-200 data-[hover=true]:bg-gray-200 data-[focus=true]:bg-gray-200" />
              </Slider>
            </Box>
            <Pressable
              className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center"
              onPress={() => {
                setDarkIntensity(0.75);
              }}
            >
              <Ionicons name="moon-outline" size={24} color="rgba(255,255,255,0.9)" />
            </Pressable>
          </Box>
        </Box>
      )}
      {isMenuOpen && (
        <Box className="absolute bottom-[68px] left-2 top-[68px] items-center justify-center">
          <Box
            className={`border border-gray-200/20  p-2 gap-2 backdrop-blur-md shadow-md shadow-black/10
           bg-black/20 pointer-events-auto items-left
           ${isMenuHover ? "rounded-[28px]" : "rounded-full"}
           `}
            onPointerEnter={() => {
              setIsMenuHover(true);
            }}
            onPointerLeave={() => {
              setIsMenuHover(false);
            }}
          >
            <Pressable className="flex-row gap-2 items-center">
              <Pressable
                className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center"
                onPress={() => {
                  setIsTakeScreenShot(true);
                }}
              >
                <Ionicons name="camera-outline" size={24} color="white" />
              </Pressable>
              <Text
                size="sm"
                className={`text-white ${isMenuHover ? "block" : "hidden"} select-none`}
              >
                Screenshot
              </Text>
            </Pressable>
            <Pressable className="flex-row gap-2 items-center">
              <Pressable
                className={`rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center
                ${isOrthographic ? "bg-gray-200" : "bg-black/20"}
                `}
                onPress={() => {
                  setIsOrthographic(!isOrthographic);
                }}
              >
                <Ionicons
                  name="cube-outline"
                  size={24}
                  color={isOrthographic ? "black" : "white"}
                />
              </Pressable>
              <Text
                size="sm"
                className={`text-white ${isMenuHover ? "block" : "hidden"} select-none`}
              >
                Orthographic view
              </Text>
            </Pressable>
            <Pressable
              className="flex-row gap-2 items-center"
              onPress={() => {
                setIsFPVMode(!isFPVMode);
              }}
            >
              <Box
                className={`rounded-full h-[38px] w-[38px] items-center justify-center
                ${isFPVMode ? "bg-gray-200" : "bg-black/20"}
                `}
              >
                <Ionicons
                  name={isFPVMode ? "walk-outline" : "walk-outline"}
                  size={24}
                  color={isFPVMode ? "black" : "white"}
                />
              </Box>
              <Text
                size="sm"
                className={`text-white ${isMenuHover ? "block" : "hidden"} select-none`}
              >
                FPV Mode
              </Text>
            </Pressable>
            <Pressable className="flex-row gap-2 items-center">
              <Pressable className="rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center">
                <Ionicons name="car" size={24} color="white" />
              </Pressable>
              <Text
                size="sm"
                className={`text-white ${isMenuHover ? "block" : "hidden"} select-none`}
              >
                Add Props
              </Text>
            </Pressable>
          </Box>
        </Box>
      )}
      <LeftBottomPanel />
      {isPartToggleOpen && <PartTogglePanel />}
      {isOrthographic && (
        <>
          <OrthographicTitles />
          <Box
            className={`border border-gray-200/20  p-2 gap-2 backdrop-blur-md shadow-md shadow-black/10
           bg-black/20 pointer-events-auto  absolute  left-2 items-left justify-center
           ${isMenuHover ? "rounded-[28px]" : "rounded-full"}
            h-auto w-fill`}
            onPointerEnter={() => {
              setIsMenuHover(true);
            }}
            onPointerLeave={() => {
              setIsMenuHover(false);
            }}
            style={{
              top: "80%",
              left: "0.1%",
            }}
          >
            <Pressable className="flex-row gap-2 items-center">
              <Pressable
                className={`rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center
                 "bg-gray-200"
                `}
                onPress={() => {
                  setResetMultiViews(true);
                }}
              >
                <Ionicons name="refresh" size={24} color="white" />
              </Pressable>
              <Text
                size="sm"
                className={`text-white ${isMenuHover ? "block" : "hidden"} select-none`}
              >
                Reset views
              </Text>
            </Pressable>

            <Pressable className="flex-row gap-2 items-center justify-right">
              <Pressable
                className={`rounded-full bg-black/20 h-[38px] w-[38px] items-center justify-center
                 "bg-gray-200"
                `}
                onPress={() => {
                  let value = multiViewsCycle + 1;
                  setMultiViewsCycle(value);
                }}
              >
                <Ionicons name="play-skip-forward" size={24} color="white" />
              </Pressable>
              <Text
                size="sm"
                className={`text-white ${
                  isMenuHover ? "block" : "hidden"
                } select-none justify-left`}
              >
                Next view
              </Text>
            </Pressable>
          </Box>
        </>
      )}
    </Box>
  );
}
