import { Box } from "@/components/ui/box";
import { CloseIcon, SearchIcon } from "@/components/ui/icon";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { getAutoCompletePlaces } from "@/utils/api/mapRequest";
import { useMapStore } from "@/utils/zustand/mapStore";
import { useEffect } from "react";

export default function PlaceAutoComplete() {
  const { setPlaces, search, setSearch } = useMapStore();
  useEffect(() => {
    async function fetchPlaces() {
      if (search) {
        const res = await getAutoCompletePlaces(search, ["address"]);
        if (res.success && res.predictions) {
          setPlaces(res.predictions);
        }
      }
    }
    fetchPlaces();
  }, [search, setPlaces]);

  return (
    <Box className="w-full sm:w-[400px] backdrop-blur-md rounded-full overflow-hidden ">
      <Input className="rounded-full overflow-hidden h-[38px] ml-[1px]">
        <InputField
          className="w-full text-white placeholder:text-white bg-transparent"
          placeholder="Search place"
          value={search}
          onChange={(e: any) => setSearch(e.target.value)}
        />
        <InputSlot
          className="pr-3 select-none"
          onPress={() => {
            if (search) setSearch("");
          }}
        >
          {search ? <InputIcon as={CloseIcon} /> : <InputIcon as={SearchIcon} />}
        </InputSlot>
      </Input>
    </Box>
  );
}
