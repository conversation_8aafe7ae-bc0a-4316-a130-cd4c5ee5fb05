import { use<PERSON>rame } from "@react-three/fiber";
import { useRef } from "react";
import * as THREE from "three";

const boxData = [
  { position: [-4, 0, -4], color: "red" },
  { position: [-2, 0, -4], color: "orange" },
  { position: [0, 0, -4], color: "yellow" },
  { position: [2, 0, -4], color: "green" },
  { position: [4, 0, -4], color: "blue" },
  { position: [-4, 0, -2], color: "indigo" },
  { position: [-2, 0, -2], color: "violet" },
  { position: [0, 0, -2], color: "pink" },
  { position: [2, 0, -2], color: "cyan" },
  { position: [4, 0, -2], color: "lime" },
];

const cylinderData = [
  { position: [-4, 0, 0], color: "brown" },
  { position: [-2, 0, 0], color: "gray" },
  { position: [0, 0, 0], color: "hotpink" },
  { position: [2, 0, 0], color: "white" },
  { position: [4, 0, 0], color: "gold" },
  { position: [-4, 0, 2], color: "silver" },
  { position: [-2, 0, 2], color: "maroon" },
  { position: [0, 0, 2], color: "olive" },
  { position: [2, 0, 2], color: "navy" },
  { position: [4, 0, 2], color: "teal" },
];

const sphereData = [
  { position: [-4, 0, 4], color: "coral" },
  { position: [-2, 0, 4], color: "crimson" },
  { position: [0, 0, 4], color: "darkblue" },
  { position: [2, 0, 4], color: "darkgreen" },
  { position: [4, 0, 4], color: "darkorange" },
  { position: [-4, 0, 6], color: "darkred" },
  { position: [-2, 0, 6], color: "goldenrod" },
  { position: [0, 0, 6], color: "lightseagreen" },
  { position: [2, 0, 6], color: "mediumorchid" },
  { position: [4, 0, 6], color: "royalblue" },
];

interface Props {
  position: number[];
  color: string;
}

function Box({ position, color }: Props) {
  const ref = useRef<THREE.Mesh>(null!);
  useFrame((_state, delta) => (ref.current.rotation.x += delta));
  return (
    <mesh
      position={new THREE.Vector3(position[0], position[1], position[2])}
      ref={ref}
      name="box"
      userData={{
        onClick: (uuid: string) => {
          console.log("click BOX", uuid);
        },
      }}
    >
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color={color} />
    </mesh>
  );
}

function Cylinder({ position, color }: Props) {
  const ref = useRef<THREE.Mesh>(null!);
  useFrame((_state, delta) => (ref.current.rotation.z += delta));
  return (
    <mesh
      position={new THREE.Vector3(position[0], position[1], position[2])}
      ref={ref}
      name="cylinder"
      userData={{
        onClick: (uuid: string) => {
          console.log("click CYLINDER", uuid);
        },
        draggable: true,
      }}
    >
      <cylinderGeometry args={[0.5, 0.5, 1, 32]} />
      <meshStandardMaterial color={color} />
    </mesh>
  );
}

function Sphere({ position, color }: Props) {
  const ref = useRef<THREE.Mesh>(null!);
  return (
    <mesh
      position={new THREE.Vector3(position[0], position[1], position[2])}
      ref={ref}
      name="sphere"
      userData={{
        onClick: (uuid: string) => {
          console.log("click SPHERE", uuid);
        },
      }}
    >
      <sphereGeometry args={[0.5, 32, 32]} />
      <meshStandardMaterial color={color} />
    </mesh>
  );
}

export default function TestElements() {
  return (
    <>
      {boxData.map((box, index) => (
        <Box key={`box-${index}`} position={box.position} color={box.color} />
      ))}
      {cylinderData.map((cylinder, index) => (
        <Cylinder
          key={`cylinder-${index}`}
          position={cylinder.position}
          color={cylinder.color}
        />
      ))}
      {sphereData.map((sphere, index) => (
        <Sphere
          key={`sphere-${index}`}
          position={sphere.position}
          color={sphere.color}
        />
      ))}
    </>
  );
}
