import { getLotBoundary } from "@/utils/api/mapRequest";
import { lngLatToMercator } from "@/utils/helpers/mapHelpers";
import { useMapStore } from "@/utils/zustand/mapStore";
import { useEffect, useMemo } from "react";
import * as THREE from "three";

export default function LotBoundary() {
  const { coordinates, lotBoundarys, setLotBoundarys, placeId } = useMapStore();
  const { x: x0, y: y0 } = lngLatToMercator(coordinates.lng, coordinates.lat);

  useEffect(() => {
    async function fetchLotBoundary() {
      if (coordinates && placeId) {
        const res = await getLotBoundary({ placeId });
        setLotBoundarys(res.lots);
      }
    }
    fetchLotBoundary();
  }, [coordinates, placeId, setLotBoundarys]);

  const shapes = useMemo(() => {
    if (lotBoundarys.length === 0) return [];
    const outers = lotBoundarys.map((lot) =>
      lot.geometry.map((ring) => ring.map(([x, y]) => new THREE.Vector2(x - x0, y - y0)))
    );
    const shapes = outers.map((outer) => new THREE.Shape(outer[0]));
    return shapes;
  }, [lotBoundarys, x0, y0]);

  const geometrys = useMemo(() => shapes.map((shape) => new THREE.ShapeGeometry(shape)), [shapes]);
  const edges = useMemo(
    () => geometrys.map((geometry) => new THREE.EdgesGeometry(geometry)),
    [geometrys]
  );

  return (
    <group position={[0, -0.33, 0]}>
      {edges.map((edge, index) => (
        <lineSegments geometry={edge} rotation={[-Math.PI / 2, 0, 0]} key={index}>
          <lineBasicMaterial color="white" linewidth={3} />
        </lineSegments>
      ))}
      {geometrys.map((geometry, index) => (
        <mesh geometry={geometry} rotation={[-Math.PI / 2, 0, 0]} key={index}>
          <meshBasicMaterial color="green" transparent opacity={0.5} side={THREE.DoubleSide} />
        </mesh>
      ))}
    </group>
  );
}
