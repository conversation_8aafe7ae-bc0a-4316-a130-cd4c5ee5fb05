import { useGLTF } from "@/components/threeLib/AssetManager";
import { Asset } from "expo-asset";
import React, { useEffect, useMemo, useRef, useState } from "react";
import * as THREE from "three";

export default function Trees({ visible = true }) {
  const [modelReady, setModelReady] = useState(false);
  const [positions, setPositions] = useState<THREE.Vector3[]>([]);
  useEffect(() => {
    const trees: THREE.Vector3[] = [];

    for (let i = 0; i < 50; i++) {
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * 1000 + 100;

      const x = Math.cos(angle) * distance;
      const z = Math.sin(angle) * distance;
      const y = -0.41;

      trees.push(new THREE.Vector3(x, y, z));
    }

    setPositions(trees);
  }, []);

  const modelRef = useRef<any>(null);
  const gltf = useGLTF(Asset.fromModule(require(`@/assets/models/tree_fall.glb`)).uri);
  useEffect(() => {
    if (!gltf || !gltf.scene) return;
    modelRef.current = gltf.scene;
    setModelReady(true);
  }, [gltf]);

  const clonedTrees = useMemo(() => {
    if (!modelReady) return [];
    return positions.map((pos) => {
      const tree = modelRef.current.clone(true);
      tree.position.copy(pos);
      return tree;
    });
  }, [modelReady, positions]);

  return (
    <group visible={visible}>
      {modelReady && clonedTrees.map((tree, index) => <mesh key={index} object={tree} />)}
    </group>
  );
}
