import { TextureLoader } from "expo-three";
import React, { useEffect, useState } from "react";
import * as THREE from "three";

function generateMountainShape(width = 100, height = 10, segments = 10) {
  const shape = new THREE.Shape();

  shape.moveTo(-width / 2, height);

  for (let i = 1; i < segments; i++) {
    const x = -width / 2 + i * (width / segments);
    const y = Math.random() * height * 0.6 + height * 0.4;
    shape.lineTo(x, y);
  }

  shape.lineTo(width / 2, height);

  shape.lineTo(width / 2, 0);
  shape.lineTo(-width / 2, 0);
  shape.lineTo(-width / 2, height);

  return new THREE.ShapeGeometry(shape);
}

export default function Mountain({ visible = true, radius = 1000, count = 10, height = 30 }) {
  const [geometryArray, setGeometryArray] = useState<THREE.ShapeGeometry[]>([]);
  useEffect(() => {
    setGeometryArray([...Array(count)].map(() => generateMountainShape(650, height, 5)));
  }, [count, height]);

  return (
    <group visible={visible}>
      {geometryArray.map((geo, i) => {
        const angle = (i / count) * Math.PI * 2;
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        const rotationY = -angle + Math.PI / 2;

        return (
          <mesh
            key={i}
            geometry={geo}
            position={[x, 0, z]}
            rotation={[0, rotationY, 0]}
            frustumCulled={false}
            renderOrder={-1}
            receiveShadow
          >
            <meshBasicMaterial
              color={0x626b60}
              transparent
              opacity={1}
              side={THREE.DoubleSide}
              depthWrite={false}
            />
          </mesh>
        );
      })}
    </group>
  );
}

export function Fog({ visible = true, radius = 1000, count = 10, height = 40 }) {
  const [geometryArray, setGeometryArray] = useState<THREE.ShapeGeometry[]>([]);
  useEffect(() => {
    setGeometryArray([...Array(count)].map(() => generateMountainShape(650, height, 5)));
  }, [count, height]);

  return (
    <group visible={visible}>
      {geometryArray.map((geo, i) => {
        const angle = (i / count) * Math.PI * 2;
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        const rotationY = -angle + Math.PI / 2;

        return (
          <mesh
            key={i}
            geometry={geo}
            position={[x, 0, z]}
            rotation={[0, rotationY, 0]}
            frustumCulled={false}
            renderOrder={-1}
            receiveShadow
          >
            <meshBasicMaterial
              color={0x626b60}
              transparent
              opacity={1}
              side={THREE.DoubleSide}
              depthWrite={false}
            />
          </mesh>
        );
      })}
    </group>
  );
}

export function RadialFog({ radius = 1000, opacity = 0.6 }) {
  const fogTex = React.useMemo(
    () =>
      new TextureLoader().load(require("@/assets/images/fog.png"), (texture) => {
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;

        texture.anisotropy = 1;
      }),
    []
  );

  return (
    <mesh rotation-x={-Math.PI / 2} position={[0, 10, 0]}>
      <circleGeometry args={[radius, 64]} />
      <meshBasicMaterial
        map={fogTex}
        transparent
        opacity={opacity}
        depthWrite={false}
        side={THREE.DoubleSide}
        blending={THREE.NormalBlending}
        toneMapped={false}
      />
    </mesh>
  );
}

export function MapEdgeFogRing({
  radius = 1000,
  height = 50,
  segments = 32,
  opacity = 0.3,
}) {
  const angleStep = (Math.PI * 2) / segments;

  const fogTex = React.useMemo(
    () =>
      new TextureLoader().load(require("@/assets/images/fog1.png"), (texture) => {
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;

        texture.anisotropy = 1;
      }),
    []
  );
  return (
    <>
      {Array.from({ length: segments }).map((_, i) => {
        const angle = i * angleStep;
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        const rotationY = -angle + Math.PI / 2;

        return (
          <mesh
            key={i}
            position={[x, 0, z]}
            rotation={[0, rotationY, 0]}
            frustumCulled={false}
            renderOrder={999}
          >
            <planeGeometry args={[radius * 0.3, height]} />
            <meshStandardMaterial
              map={fogTex}
              transparent
              opacity={opacity}
              depthWrite={false}
              side={THREE.DoubleSide}
            />
          </mesh>
        );
      })}
    </>
  );
}
