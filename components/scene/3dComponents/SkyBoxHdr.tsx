import { useRGBE } from "@/components/threeLib/AssetManager";
import { useSceneStore } from "@/utils/zustand/sceneStore";
import { useThree } from "@react-three/fiber";
import React, { useEffect, useMemo, useState } from "react";
import * as THREE from "three";
import { useShallow } from "zustand/react/shallow";

export default function SkyBoxHdr() {
  const darkIntensity = useSceneStore(useShallow((s) => s.darkIntensity));
  const [skyBoxIndex, setSkyBoxIndex] = useState(-1);
  const { isOrthographic } = useSceneStore();
  const lightIntensity = useMemo(() => {
    return 1 - darkIntensity;
  }, [darkIntensity]);
  const { scene, gl } = useThree();
  const day = useRGBE(require("@/assets/images/sky.hdr"));
  const sunset = useRGBE(require("@/assets/images/sunset.hdr"));
  const night = useRGBE(require("@/assets/images/night.hdr"));

  useEffect(() => {
    if (day) {
      day.mapping = THREE.EquirectangularReflectionMapping;
    }
    if (sunset) {
      sunset.mapping = THREE.EquirectangularReflectionMapping;
    }
    if (night) {
      night.mapping = THREE.EquirectangularReflectionMapping;
    }
    gl.setClearAlpha(1);
    gl.outputColorSpace = THREE.SRGBColorSpace;
    gl.toneMapping = THREE.ACESFilmicToneMapping;
  }, [day, sunset, night, gl]);

  useEffect(() => {
    if (lightIntensity > 0.6) {
      gl.toneMappingExposure = 1;
      if (skyBoxIndex !== 0 && day) {
        setSkyBoxIndex(0);
        scene.environment = day;
        scene.background = day;
      }
    }
    if (lightIntensity <= 0.6 && lightIntensity > 0.25) {
      gl.toneMappingExposure = 0.5;
      if (skyBoxIndex !== 1 && sunset) {
        setSkyBoxIndex(1);
        scene.environment = sunset;
        scene.background = sunset;
      }
    }
    if (lightIntensity <= 0.25) {
      gl.toneMappingExposure = 1;
      if (skyBoxIndex !== 2 && night) {
        setSkyBoxIndex(2);
        scene.environment = night;
        scene.background = night;
      }
    }
    if (isOrthographic) {
      setSkyBoxIndex(-1);
      scene.background = new THREE.Color(0xeeeeee);
    }
  }, [lightIntensity, gl, day, sunset, night, scene, skyBoxIndex, isOrthographic]);

  return (
    <>
      <ambientLight intensity={(Math.PI / 4) * lightIntensity} />
      <directionalLight
        position={[10, 10, 10]}
        intensity={lightIntensity > 0.25 ? Math.PI * lightIntensity : 0}
        castShadow
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
        shadow-camera-near={1}
        shadow-camera-far={50}
      />
    </>
  );
}
