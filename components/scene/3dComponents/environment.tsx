import { Euler, applyProps, useThree } from '@react-three/fiber';
import * as React from 'react';
import * as THREE from 'three';

export type EnvironmentProps = {
  background?: boolean | 'only';

  backgroundBlurriness?: number;
  backgroundIntensity?: number;
  backgroundRotation?: Euler;
  environmentIntensity?: number;
  environmentRotation?: Euler;

  files: readonly string[];
};

function setEnvProps(
  background: boolean | 'only',
  defaultScene: THREE.Scene,
  texture: THREE.Texture,
  sceneProps: Partial<EnvironmentProps> = {},
) {
  sceneProps = {
    backgroundBlurriness: 0,
    backgroundIntensity: 1,
    backgroundRotation: [0, 0, 0],
    environmentIntensity: 1,
    environmentRotation: [0, 0, 0],
    ...sceneProps,
  };

  const target = defaultScene;
  const oldbg = target.background;
  const oldenv = target.environment;
  const oldSceneProps = {
    backgroundBlurriness: target.backgroundBlurriness,
    backgroundIntensity: target.backgroundIntensity,
    backgroundRotation: target.backgroundRotation?.clone?.() ?? [0, 0, 0],
    environmentIntensity: target.environmentIntensity,
    environmentRotation: target.environmentRotation?.clone?.() ?? [0, 0, 0],
  };
  target.environment = texture;
  if (background) target.background = texture;
  applyProps(target as any, sceneProps);

  return () => {
    if (background !== 'only') target.environment = oldenv;
    if (background) target.background = oldbg;
    applyProps(target as any, oldSceneProps);
  };
}

function Environment({
  background = false,
  files,
  backgroundBlurriness,
  backgroundIntensity,
  backgroundRotation,
  environmentIntensity,
  environmentRotation,
}: EnvironmentProps) {
  const defaultScene = useThree((state) => state.scene);
  const texture = React.useMemo(() => new THREE.CubeTextureLoader().load(files), [files]);

  React.useLayoutEffect(() => {
    return setEnvProps(background, defaultScene, texture, {
      backgroundBlurriness,
      backgroundIntensity,
      backgroundRotation,
      environmentIntensity,
      environmentRotation,
    });
  });

  React.useEffect(() => {
    return () => {
      texture.dispose();
    };
  }, [texture]);

  return null;
}

const EnvironmentMemo = React.memo(Environment);

export { EnvironmentMemo as Environment };
