import * as THREE from "three";

import { TextureLoader } from "expo-three";
import React from "react";

type GrassProps = {
  visible?: boolean;
};

function Grass({ visible = true }: GrassProps) {
  const grass = React.useMemo(
    () =>
      new TextureLoader().load(require("@/assets/images/grass3.jpg"), (texture) => {
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(25, 25);
        texture.anisotropy = 1;
      }),
    []
  );

  return (
    <mesh visible={visible} position-y={-0.401} rotation-x={-Math.PI / 2} receiveShadow>
      <circleGeometry args={[1200, 1200]} />
      <meshStandardMaterial map={grass} />
    </mesh>
  );
}

const GrassMemo = React.memo(Grass);

export default GrassMemo;
