import { useGLTF } from "@/components/threeLib/AssetManager";
import { invalidate } from "@react-three/fiber";
import { Asset } from "expo-asset";
import { TextureLoader } from "expo-three";
import React, { useMemo, useRef } from "react";
import * as THREE from "three";

interface Boundary {
  min: THREE.Vector3;
  max: THREE.Vector3;
}

export default function Grass({
  visible = true,
  boundary = {
    min: new THREE.Vector3(-5, -1, -5),
    max: new THREE.Vector3(5, 1, 5),
  },
}: {
  visible?: boolean;
  boundary?: Boundary;
}) {
  const gltf = useGLTF(Asset.fromModule(require(`@/assets/models/grass_22.glb`)).uri);
  const boundaryRef = useRef<THREE.Box3>(new THREE.Box3());
  const instancedRef = useRef<THREE.InstancedMesh>(null);

  React.useEffect(() => {
    boundaryRef.current.setFromCenterAndSize(
      boundary.min.clone().add(boundary.max).multiplyScalar(0.5),
      boundary.max.clone().sub(boundary.min)
    );
  }, [boundary]);

  const originalPositionsRef = useRef<THREE.Vector3[]>([]);
  if (originalPositionsRef.current.length === 0) {
    const grass: THREE.Vector3[] = [];
    for (let i = 0; i < 5000; i++) {
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * 300;

      const x = Math.cos(angle) * distance;
      const z = Math.sin(angle) * distance;
      const y = -0.41;

      grass.push(new THREE.Vector3(x, y, z));
    }
    originalPositionsRef.current = grass;
  }

  const positions = useMemo(() => {
    return originalPositionsRef.current.filter((pos) => !boundaryRef.current.containsPoint(pos));
  }, [originalPositionsRef, boundary]);

  const instancedMesh = useMemo(() => {
    if (!gltf?.scene) return null;

    const originalMesh = gltf.scene.getObjectByProperty("type", "Mesh") as THREE.Mesh;

    if (!originalMesh || !originalMesh.geometry || !originalMesh.material) return null;

    if (Array.isArray(originalMesh.material)) {
      originalMesh.material.forEach((mat) => {
        mat.transparent = true;
        mat.opacity = 0.8;
      });
    } else {
      originalMesh.material.transparent = true;
      originalMesh.material.opacity = 0.5;
    }

    const instanced = new THREE.InstancedMesh(
      originalMesh.geometry,
      originalMesh.material,
      positions.length
    );

    const dummy = new THREE.Object3D();

    positions.forEach((pos, i) => {
      dummy.position.copy(pos);

      dummy.rotation.x = -Math.PI / 2;
      // dummy.rotation.z = (Math.PI / 2) * Math.random();

      dummy.updateMatrix();
      instanced.setMatrixAt(i, dummy.matrix);
    });

    instanced.instanceMatrix.needsUpdate = true;
    return instanced;
  }, [gltf, positions]);

  return (
    <group visible={visible}>
      {instancedMesh && <primitive ref={instancedRef} object={instancedMesh} />}
      <Grass2 visible={visible} boundary={boundary} />
    </group>
  );
}

function Grass2({
  visible = true,
  boundary = {
    min: new THREE.Vector3(-5, -1, -5),
    max: new THREE.Vector3(5, 1, 5),
  },
}: {
  visible?: boolean;
  boundary?: Boundary;
}) {
  const gltf = useGLTF(Asset.fromModule(require(`@/assets/models/grass_shape.glb`)).uri);
  const boundaryRef = useRef<THREE.Box3>(new THREE.Box3());
  const instancedRef = useRef<THREE.InstancedMesh>(null);

  React.useEffect(() => {
    boundaryRef.current.setFromCenterAndSize(
      boundary.min.clone().add(boundary.max).multiplyScalar(0.5),
      boundary.max.clone().sub(boundary.min)
    );
  }, [boundary]);

  const originalPositionsRef = useRef<THREE.Vector3[]>([]);
  if (originalPositionsRef.current.length === 0) {
    const grass: THREE.Vector3[] = [];
    for (let i = 0; i < 500; i++) {
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * 300;

      const x = Math.cos(angle) * distance;
      const z = Math.sin(angle) * distance;
      const y = -0.41;

      grass.push(new THREE.Vector3(x, y, z));
    }
    originalPositionsRef.current = grass;
  }

  const positions = useMemo(() => {
    return originalPositionsRef.current.filter((pos) => !boundaryRef.current.containsPoint(pos));
  }, [originalPositionsRef, boundary]);

  const instancedMesh = useMemo(() => {
    if (!gltf?.scene) return null;

    const originalMesh = gltf.scene.getObjectByProperty("type", "Mesh") as THREE.Mesh;

    if (!originalMesh || !originalMesh.geometry || !originalMesh.material) return null;

    // load texture for grass2
    const texture = new TextureLoader().load(
      require("@/assets/images/Image_1.005.png"),
      (texture) => {
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
      }
    );
    if (Array.isArray(originalMesh.material)) {
      originalMesh.material.forEach((mat) => {
        mat.transparent = true;
        mat.opacity = 0.8;
        if ("map" in mat) {
          (mat as THREE.MeshStandardMaterial).map = texture;
        }
      });
    } else {
      originalMesh.material.transparent = true;
      originalMesh.material.opacity = 0.5;
      if ("map" in originalMesh.material) {
        (originalMesh.material as THREE.MeshStandardMaterial).map = texture;
      }
    }

    const instanced = new THREE.InstancedMesh(
      originalMesh.geometry,
      originalMesh.material,
      positions.length
    );

    const dummy = new THREE.Object3D();

    positions.forEach((pos, i) => {
      dummy.position.copy(pos);
      dummy.rotation.y = Math.random() * Math.PI * 2;
      dummy.updateMatrix();
      instanced.setMatrixAt(i, dummy.matrix);
    });

    instanced.instanceMatrix.needsUpdate = true;
    invalidate();
    return instanced;
  }, [gltf, positions]);

  return (
    <group visible={visible}>
      {instancedMesh && <primitive ref={instancedRef} object={instancedMesh} />}
    </group>
  );
}
