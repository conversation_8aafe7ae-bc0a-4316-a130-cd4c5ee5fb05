import * as THREE from "three";

import { useThreeInternalStore } from "@/components/threeLib/zustand/threeStore";
import { lngLatToMercator, lngLatToTile } from "@/utils/helpers/mapHelpers";
import { useMapStore } from "@/utils/zustand/mapStore";
import { TextureLoader } from "expo-three";
import { useEffect, useState } from "react";

const mapTilerApiKey = process.env.EXPO_PUBLIC_MAPTILER_API_KEY;

function getTileMeters(zoom: number) {
  const EARTH_RADIUS = 6378137; // in meters
  const shift = 2 * Math.PI * EARTH_RADIUS;
  const numTiles = 1 << zoom;
  return shift / numTiles;
}

function tileXYToBounds(tileX: number, tileY: number, zoom: number) {
  const EARTH_RADIUS = 6378137; // in meters
  const ORIGIN_SHIFT = (2 * Math.PI * EARTH_RADIUS) / 2;

  const tileMeters = getTileMeters(zoom);

  const minX = tileX * tileMeters - ORIGIN_SHIFT;
  const maxX = (tileX + 1) * tileMeters - ORIGIN_SHIFT;
  const maxY = ORIGIN_SHIFT - tileY * tileMeters;
  const minY = ORIGIN_SHIFT - (tileY + 1) * tileMeters;

  return {
    bottomLeft: [minX, minY], // (x, y) in Web Mercator
    bottomRight: [maxX, minY],
    topLeft: [minX, maxY],
    topRight: [maxX, maxY],
    center: [minX + tileMeters / 2, minY + tileMeters / 2],
  };
}

function getTileUrl(zoom: number, tileX: number, tileY: number, layerType: "satellite" | "street") {
  if (layerType === "satellite") {
    const url = `https://api.maptiler.com/maps/satellite/${zoom}/${tileX}/${tileY}@2x.jpg?key=${mapTilerApiKey}`;
    return url;
  } else {
    const url = `https://tile.openstreetmap.org/${zoom}/${tileX}/${tileY}.png`;
    // const url = `https://api.maptiler.com/maps/streets-v2/${zoom}/${tileX}/${tileY}@2x.png?key=${mapTilerApiKey}`;
    return url;
  }
}

// generate a single satellite tile
function SatelliteTile({
  zoom,
  tileX,
  tileY,
  position,
  layerType,
}: {
  zoom: number;
  tileX: number;
  tileY: number;
  position: [number, number, number];
  layerType: "satellite" | "street";
}) {
  const tileMeters = getTileMeters(zoom);
  const tileUrl = getTileUrl(zoom, tileX, tileY, layerType);

  const { isDirty } = useThreeInternalStore();
  const [texture, setTexture] = useState<THREE.Texture | null>(null);

  useEffect(() => {
    const loader = new TextureLoader();
    loader.load(tileUrl, (t) => {
      setTexture(t);
    });
    isDirty.value = true;
  }, [tileUrl, isDirty]);

  return (
    <mesh position={position} rotation={[-Math.PI / 2, 0, 0]}>
      <planeGeometry args={[tileMeters, tileMeters]} />
      {texture && <meshStandardMaterial map={texture} />}
    </mesh>
  );
}

// render n*n tiles around the center tile
// 152.576 is the size of each tile in world units
export default function MapTilesPlane() {
  const zoom = 19;
  const { coordinates, mapLayerType } = useMapStore();
  const { lat, lng } = coordinates;
  const [tileX, tileY] = lngLatToTile(lng, lat, zoom);
  const mercator = lngLatToMercator(lng, lat);
  const { center } = tileXYToBounds(tileX, tileY, zoom);
  const offsetX = center[0] - mercator.x;
  const offsetY = center[1] - mercator.y;

  const tiles = [];
  const tile_number = 6;
  const n = Math.floor(tile_number / 2);
  const tileMeters = getTileMeters(zoom);
  for (let dx = -n; dx <= n; dx++) {
    for (let dy = -n; dy <= n; dy++) {
      tiles.push({
        x: tileX + dx,
        y: tileY + dy,
        position: [dx * tileMeters, -0.35, dy * tileMeters],
      });
    }
  }

  return (
    <group position={[offsetX, 0, -offsetY]}>
      {tiles.map(({ x, y, position }, index) => (
        <SatelliteTile
          key={`${x}-${y}`}
          tileX={x}
          tileY={y}
          zoom={zoom}
          position={position}
          layerType={mapLayerType}
        />
      ))}
    </group>
  );
}
