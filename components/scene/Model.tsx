import { useScreenshot } from "@/hooks/useScreenshot";
import { getAutoScreenshotCamPostion, getLocalTime, setGltf } from "@/utils/utils";
import { useSceneStore } from "@/utils/zustand/sceneStore";
import { useScreenShootsStore } from "@/utils/zustand/screenShootsStore";
import { invalidate, useFrame, useThree } from "@react-three/fiber";
import { Asset } from "expo-asset";
import { useEffect, useRef, useState } from "react";
import * as THREE from "three";
import { useGLTF } from "../threeLib/AssetManager";
import { useThreeInternalStore } from "../threeLib/zustand/threeStore";

export default function Model({ url }: { url: string }) {
  let resolvedModelPath = "";
  if (url === "") {
    resolvedModelPath = Asset.fromModule(require(`@/assets/models/Test_Shed_Request.glb`)).uri;
  } else {
    resolvedModelPath = url;
  }

  const gltf = useGLTF(resolvedModelPath);
  const [modelReady, setModelReady] = useState(false);
  const { isDirty, camera: cameraPosition, target: cameraTarget } = useThreeInternalStore();
  const modelRef = useRef<any>(null);
  const { scene, gl, camera } = useThree();
  const { hiddenParts, isOrthographic } = useSceneStore();

  const { isTakeScreenShot, setIsTakeScreenShot, isAutoshoots, setIsAutoshoots } =
    useScreenShootsStore();
  const { takeScreenShot } = useScreenshot(gl);

  const mixer = useRef<THREE.AnimationMixer>(null);
  const lastTimeRef = useRef(performance.now());
  const actionsRecord = useRef<
    Record<string, { action: THREE.AnimationAction; currentTime: number; reverse: boolean }>
  >({});

  useEffect(() => {
    if (!gltf || !gltf.scene) return;

    mixer.current = new THREE.AnimationMixer(gltf.scene);

    const animationTargets = {};
    gltf.animations.forEach((clip) => {
      clip.tracks.forEach((track) => {
        const trackName = track.name.split(".")[0];
        animationTargets[trackName] = clip.name;
      });
    });

    gltf.animations.forEach((clip) => {
      const action = mixer.current?.clipAction(clip);
      if (action) {
        action.clampWhenFinished = true;
        action.paused = true;
        actionsRecord.current[clip.name] = {
          action,
          currentTime: 0,
          reverse: false,
        };
      }
    });

    gltf.scene.traverse((child) => {
      child.castShadow = true;
      child.receiveShadow = true;
      if (child.name in animationTargets) {
        child.userData.animation = animationTargets[child.name];
      }
      if (child instanceof THREE.Mesh) {
        if (child.material) {
          const materialName = child.material.name.toLowerCase();
          const [type, color] = materialName.split("-");

          if (materialName.includes("glass")) {
            child.material.color.set(0x88ffef);
            child.material.transparent = true;
            child.material.opacity = 0.15;
            child.material.metalness = 1;
            child.material.roughness = 0;
            child.material.envMapIntensity = 1.2;
          }
          if (materialName.includes("steel")) {
            child.material.color.set(0xe3e8ec);
            child.material.transparent = false;
            child.material.opacity = 1;
            child.material.metalness = 1;
            child.material.roughness = 0.5;
            child.material.envMapIntensity = 1.5;
          }
          if (color in colorBond) {
            child.material.color.set(colorBond[color]);
          }
        }
      }
    });
    modelRef.current = gltf.scene;
    setGltf(gltf);
    setModelReady(true);
  }, [gltf, isOrthographic]);

  // play animation on each frame
  useFrame((_state, delta) => {
    if (mixer.current) {
      let update = false;
      const deltaTime = Math.min(0.017, delta);
      lastTimeRef.current = lastTimeRef.current + deltaTime;
      Object.values(actionsRecord.current).forEach((record) => {
        if (record.action.paused === false || record.action.isRunning()) {
          update = true;
          const newTime = record.currentTime + deltaTime;
          record.action.time = newTime;
          record.currentTime = newTime;
        }
      });
      if (update) {
        mixer.current.update(deltaTime);
        invalidate();
      }
    }
  });

  // hide parts based on hiddenParts
  useEffect(() => {
    if (!modelRef.current) return;
    modelRef.current.traverse((child) => {
      const lowerName = child.name.toLowerCase();
      const shouldHide = hiddenParts.some((part) => lowerName.includes(part));
      child.visible = !shouldHide;
    });
    invalidate();
  }, [hiddenParts, modelRef]);

  useEffect(() => {
    if (isTakeScreenShot) {
      setIsTakeScreenShot(false);

      const time = getLocalTime();
      takeScreenShot("3D_" + time + ".png", false);
    }
    if (isAutoshoots) {
      setIsAutoshoots(false);

      let x = 0;

      const [camPostions, boundingBoxSize] = getAutoScreenshotCamPostion();

      while (x < camPostions.length) {
        async function waitforFirstRender() {
          const myPromise = new Promise((resolve) => {
            setTimeout(
              (counter: number) => {
                cameraPosition.value = camPostions[counter];
                cameraTarget.value = {
                  x: boundingBoxSize.x / 2,
                  y: boundingBoxSize.y / 2,
                  z: -boundingBoxSize.z / 2,
                };

                gl.render(scene, camera);
                isDirty.value = true;

                setTimeout(
                  (counter: number) => {
                    const time = getLocalTime();
                    gl.render(scene, camera);
                    isDirty.value = true;
                    takeScreenShot("3D_" + time + "_" + counter + ".jpg", true);
                    console.log(camPostions.length);
                    if (counter === camPostions.length - 1) {
                      setTimeout(
                        (counter: number) => {
                          window.close();
                        },
                        200,
                        counter
                      );
                    }
                  },
                  100,
                  counter
                );
                resolve("download completed");
              },
              500 + x * 500,
              x
            );
          });

          myPromise.then(
            (data) => {
              console.log("Success:", data); // Executed on resolve
            },
            (error) => {
              console.error("Error:", error.message); // Executed on reject
            }
          );
        }

        waitforFirstRender();
        x = x + 1;
      }
    }
  }, [isTakeScreenShot, isAutoshoots]);

  return (
    <group>
      {modelReady && (
        <primitive
          object={modelRef.current}
          onClick={(e) => {
            e.stopPropagation();
            const clickedObject = e.object;
            if (clickedObject.userData.animation) {
              console.log("animation:", clickedObject.userData.animation);
              const animation = clickedObject.userData.animation;
              if (actionsRecord.current[animation].action.isRunning()) {
                actionsRecord.current[animation].action.paused = true;
                actionsRecord.current[animation].currentTime =
                  actionsRecord.current[animation].action.time;
              } else {
                actionsRecord.current[animation].action.time =
                  actionsRecord.current[animation].currentTime;
                actionsRecord.current[animation].action.paused = false;
                actionsRecord.current[animation].action.play();
              }
              invalidate();
            }
          }}
        />
      )}
    </group>
  );
}

const colorBond: Record<string, THREE.Color> = {
  zincalume: new THREE.Color(0x999999),
  basalt: new THREE.Color(0x6d6c6e),
  bluegum: new THREE.Color(0x969799),
  bushland: new THREE.Color(0x8a8a7f),
  classic_cream: new THREE.Color(0xe9dcb8),
  cottage_green: new THREE.Color(0x304c3c),
  cove: new THREE.Color(0xa59f8a),
  dover_white: new THREE.Color(0xf9fbf1),
  deep_ocean: new THREE.Color(0x364152),
  dune: new THREE.Color(0xb1ada3),
  evening_haze: new THREE.Color(0xc5c2aa),
  gully: new THREE.Color(0x857e73),
  headland: new THREE.Color(0x975540),
  ironstone: new THREE.Color(0x3e434c),
  jasper: new THREE.Color(0x6c6153),
  loft: new THREE.Color(0x44393d),
  mangrove: new THREE.Color(0x737562),
  manor_red: new THREE.Color(0x5e1d0e),
  monument: new THREE.Color(0x323233),
  night_sky: new THREE.Color(0x000000),
  pale_eucalypt: new THREE.Color(0x7c846a),
  paperbark: new THREE.Color(0xcabfa4),
  sandbank: new THREE.Color(0xd1b988),
  shale_grey: new THREE.Color(0xbdbfba),
  surfmist: new THREE.Color(0xe4e2d5),
  terrain: new THREE.Color(0x67432e),
  wallaby: new THREE.Color(0x7f7c78),
  wilderness: new THREE.Color(0x65796d),
  windspray: new THREE.Color(0x888b8a),
  woodland_grey: new THREE.Color(0x4b4c46),
};
