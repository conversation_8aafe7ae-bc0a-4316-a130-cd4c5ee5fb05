import ThreeEventsHandler from "@/components/threeLib/ThreeEventsHandler";
import { useCarportStore } from "@/utils/zustand/carportStore";
import { useMapStore } from "@/utils/zustand/mapStore";
import { useSceneStore } from "@/utils/zustand/sceneStore";
import { useThree } from "@react-three/fiber";
import React, { useEffect } from "react";
import CameraControls from "../threeLib/CameraControls";
import FpvControls from "../threeLib/FpvControls";
import MultiViewsControls from "../threeLib/MultiViewsControls";
import OrbitControls from "../threeLib/OrbitControls";
import { useThreeInternalStore } from "../threeLib/zustand/threeStore";
import Grass from "./3dComponents/Grass";
import LotBoundary from "./3dComponents/LotBoundary";
import Mountain from "./3dComponents/Mountain";
import SkyBoxHdr from "./3dComponents/SkyBoxHdr";
import Trees from "./3dComponents/Trees";
import Model from "./Model";

//! Put any component that use expo-three on bottom of import
import GrassPlane from "@/components/scene/3dComponents/GrassPlane";
import MapTilesPlane from "@/components/scene/3dComponents/MapTilesPlane";

export default function Scene() {
  const { gl, size, camera } = useThree();
  const { modelId } = useCarportStore();
  const { isDirty, target:cameraTarget, camera:cameraPosition } = useThreeInternalStore();
  const modelUrl = `https://shedkit-cicd-709958899655.australia-southeast1.run.app/api/shed/Model/${modelId}.glb`;

  const { isMapOpen } = useMapStore();
  const { isOrthographic, isFPVMode } = useSceneStore();

  useEffect(() => {
    if (!isOrthographic) {
      gl.setScissorTest(false);
      gl.setViewport(0, 0, size.width, size.height);
    }
    isDirty.value = true;
  }, [isOrthographic, gl, size, isDirty]);

  useEffect(() => {
    if (isFPVMode) {
      camera.position.set(camera.position.x, 1.5, camera.position.z);
      camera.lookAt(0, 1.5, 0);
      cameraPosition.value = { x: camera.position.x, y: camera.position.y, z: camera.position.z };
      cameraTarget.value = { x: 0, y: 1.5, z: 0 };
      camera.fov = 50;
      isDirty.value = true;
    } else {
      camera.position.set(-10, 5, 10);
      camera.lookAt(0, 0, 0);
      camera.fov = 75;
      cameraTarget.value = { x: 0, y: 0, z: 0 };
      cameraPosition.value = { x: -10, y: 5, z: 10 };
      isDirty.value = true;
    }
    camera.updateProjectionMatrix();
  }, [isFPVMode, camera]);

  return (
    <>
      {isFPVMode && <FpvControls />}
      {!isFPVMode && <OrbitControls maxDistance={100} minDistance={1} />}
      <Model url={modelId === "" ? "" : modelUrl} />
      <SkyBoxHdr />
      <Grass visible={!isOrthographic} />
      <GrassPlane visible={!isOrthographic} />
      <Mountain visible={!isOrthographic} />
      <Trees visible={!isOrthographic} />
      {isMapOpen && <MapTilesPlane />}
      {isMapOpen && <LotBoundary />}
      <ThreeEventsHandler />
      {isOrthographic ? <MultiViewsControls /> : <CameraControls />}
    </>
  );
}
