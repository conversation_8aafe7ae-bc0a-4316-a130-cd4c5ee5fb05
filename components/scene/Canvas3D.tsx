import Scene from "@/components/scene/Scene";
import { Canvas } from "@react-three/fiber";
import * as THREE from "three";

import { useSceneStore } from "@/utils/zustand/sceneStore";
import React from "react";
import { StyleSheet, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ThreeEventsDetector from "../threeLib/ThreeEventsDetector";

export default function Canvas3D() {
  const { isOrthographic } = useSceneStore();
  return (
    <SafeAreaView style={styles.container}>
      <ThreeEventsDetector>
        <Canvas
          shadows
          gl={{
            preserveDrawingBuffer: true,
            debug: { checkShaderErrors: false, onShaderError: null },
            antialias: true,
          }}
          style={StyleSheet.absoluteFillObject}
          camera={{
            position: [0, 5, 10],
            far: 2500,
          }}
          frameloop="demand"
          onCreated={(state) => {
            const gl = state.gl;
            gl.shadowMap.enabled = true;
            gl.shadowMap.type = THREE.PCFSoftShadowMap;
            const _gl = gl.getContext();
            const pixelStorei = _gl.pixelStorei.bind(_gl);
            _gl.pixelStorei = function (...args) {
              const [parameter] = args;
              switch (parameter) {
                case _gl.UNPACK_FLIP_Y_WEBGL:
                  return pixelStorei(...args);
              }
            };
          }}
        >
          <Scene />
        </Canvas>
        {isOrthographic && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "transparent",
            }}
          />
        )}
      </ThreeEventsDetector>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  canvas: {
    ...StyleSheet.absoluteFillObject,
  },
});
