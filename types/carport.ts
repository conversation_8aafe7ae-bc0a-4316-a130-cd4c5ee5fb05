import { ColoursType } from '@/constants/materialColours';
import * as THREE from 'three';

export const roofTypes = ['Flat', 'Attached', 'Gable', 'Awning'] as const;
export type RoofType = (typeof roofTypes)[number];

export const openingTypes = [
  'frame_only',
  'padoor',
  'rollerdoor',
  'slidingdoor',
  'glasswindow',
  'window',
] as const;
export type OpeningType = (typeof openingTypes)[number];

export const openingLabels: Record<OpeningType, string> = {
  padoor: 'PA Door',
  rollerdoor: 'Roller Door',
  frame_only: 'Frame-Only',
  window: 'Window',
  glasswindow: 'Glass Window',
  slidingdoor: 'Sliding Door',
};

export const Constant = {
  MAX_SPAN: 7000,
  MAX_LENGTH: 12000,
  MAX_HEIGHT: 4000,
  MIN_SPAN: 1000,
  MIN_LENGTH: 1000,
  MIN_HEIGHT: 1000,
  MIN_OVERHANG: 0,
  MAX_OVERHANG: 1000,
  MIN_LEANTO_LENGTH: 1000,
  MAX_LEANTO_LENGTH: 6000,
  MIN_LEANTO_OFFSET: 0,
  MAX_LEANTO_OFFSET: 2000,
};

export type Opening = {
  x: number;
  y: number;
  width: number;
  height: number;
  type: OpeningType;
  invertNormal?: boolean;
};

export type Wall = {
  visible: boolean;
  openings: Opening[];
};

export const bayOrientations = ['front', 'left', 'back', 'right'] as const;
export type BayOrientation = (typeof bayOrientations)[number];

export type Bay = {
  mezzanine: {
    visible: boolean;
    // material: string;
  };
  walls: {
    front?: Wall;
    left: Wall;
    back: Wall;
    right: Wall;
  };
};

export function createBay(index: number, current?: Bay): Bay {
  return {
    mezzanine: current?.mezzanine ?? {
      visible: false,
    },
    walls: current?.walls ?? {
      front: index === 0 ? { visible: false, openings: [] } : undefined,
      left: { visible: false, openings: [] },
      back: { visible: false, openings: [] },
      right: { visible: false, openings: [] },
    },
  };
}

export type LeanTo = {
  visible: boolean;
  length: number;
  offset: number;
};

export interface CarportState {
  length: number;
  height: number;
  span: number;
  pitch: number;
  overhang: number;
  roofType: RoofType;
  leftLeanTo: LeanTo;
  rightLeanTo: LeanTo;
  wallColour: ColoursType;
  bays: Bay[];
  mezzanineHeight: number;
  mezzanineMaterial?: ColoursType;
}

export interface PieceGeometry {
  geo_x: number;
  geo_y: number;
  geo_z?: number;
  flange_thickness?: number;
  web_thickness?: number;
  extended?: any;
  shapeParams?: any;
}

export interface ShapeGeometry {
  shape: THREE.Shape;
  length: number;
}

export interface PiecePosition {
  x: number;
  y: number;
  z: number;
  rot_x?: number;
  rot_y?: number;
  rot_z?: number;
}

export interface CarportGroupSpatialData {
  geo: PieceGeometry[];
  positions: PiecePosition[];
  description?: string;
}

export enum CarportGroupType {
  Post = 'post',
  Rafter = 'rafter',
  Footing = 'footing',
  Slab = 'slab',
  Mezzanine = 'mezzanine',
  Roof = 'roof',
  Wall = 'wall',
  BayWall = 'baywall',
  ApexBrace = 'apexbrace',
  KneeBrace = 'kneebrace',
  Flashing = 'flashing',
}

export enum WallOpeningType {
  No = 'no_opening',
  Door = 'door',
  Window = 'window',
}

export type CarportConfig = { [key in CarportGroupType]?: CarportGroupSpatialData }; // return type for Carportconfig.ts

export type CarportOptionalConfig = { [key in CarportGroupType]?: CarportGroupSpatialData }; // return type for OptionalCarportConfig.ts
